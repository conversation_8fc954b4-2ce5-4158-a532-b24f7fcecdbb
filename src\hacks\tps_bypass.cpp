#include "../includes.hpp"
#include <Geode/modify/GJBaseGameLayer.hpp>

class $modify(GJBaseGameLayer) {

    void update(float dt) {
        auto& g = Global::get();

        // Si el autoclicker está activado, usar delta time normal para evitar conflictos
        if (g.autoclicker) {
            return GJBaseGameLayer::update(dt);
        }

        if (!g.tpsEnabled) return GJBaseGameLayer::update(dt);
        if (Global::getTPS() == 240.f) return GJBaseGameLayer::update(dt);
        if (!PlayLayer::get()) return GJBaseGameLayer::update(dt);

        float newDt = 1.f / Global::getTPS();

        if (g.frameStepper) return GJBaseGameLayer::update(newDt);

        // Optimización: limitar el número de iteraciones para evitar lag
        float realDt = dt + g.leftOver;
        if (realDt > dt && newDt < dt) realDt = dt;

        auto startTime = std::chrono::high_resolution_clock::now();
        int mult = static_cast<int>(realDt / newDt);
        
        // Límite máximo de iteraciones para evitar lag
        const int MAX_ITERATIONS = 10;
        if (mult > MAX_ITERATIONS) mult = MAX_ITERATIONS;

        for (int i = 0; i < mult; ++i) {
            GJBaseGameLayer::update(newDt);
            
            // Límite de tiempo más estricto para evitar lag
            if (std::chrono::high_resolution_clock::now() - startTime > std::chrono::duration<double, std::milli>(8.333f)) {
                mult = i + 1;
                break;
            }
        }

        g.leftOver += (dt - newDt * mult);

    }

    float getModifiedDelta(float dt) {
        auto& g = Global::get();
        
        // Si el autoclicker está activado, usar delta time normal
        if (g.autoclicker) {
            return GJBaseGameLayer::getModifiedDelta(dt);
        }

        if (!g.tpsEnabled) return GJBaseGameLayer::getModifiedDelta(dt);
        if (Global::getTPS() == 240.f) return GJBaseGameLayer::getModifiedDelta(dt);
        if (!PlayLayer::get()) return GJBaseGameLayer::getModifiedDelta(dt);

        double dVar1;
        float fVar2;
        float fVar3;
        double dVar4;

        float newDt = 1.f / Global::getTPS();
        
        if (0 < m_resumeTimer) {
            m_resumeTimer--;
            dt = 0.0;
        }

        fVar2 = 1.0;
        if (m_gameState.m_timeWarp <= 1.0) {
            fVar2 = m_gameState.m_timeWarp;
        }

        dVar1 = dt + m_extraDelta;
        fVar3 = std::round(dVar1 / (fVar2 * newDt));
        dVar4 = fVar3 * fVar2 * newDt;
        m_extraDelta = dVar1 - dVar4;

        return dVar4;
    }

};