<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>frames</key>
	<dict>
		<key>geode.custom-keybinds/controllerBtn_LB_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{1,0}</string>
			<key>spriteSize</key>
			<string>{116,83}</string>
			<key>spriteSourceSize</key>
			<string>{118,84}</string>
			<key>textureRect</key>
			<string>{{0,0},{116,83}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
		<key>geode.custom-keybinds/controllerBtn_LT_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{1,0}</string>
			<key>spriteSize</key>
			<string>{116,83}</string>
			<key>spriteSourceSize</key>
			<string>{118,84}</string>
			<key>textureRect</key>
			<string>{{0,85},{116,83}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
		<key>geode.custom-keybinds/controllerBtn_RB_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{1,0}</string>
			<key>spriteSize</key>
			<string>{116,83}</string>
			<key>spriteSourceSize</key>
			<string>{118,84}</string>
			<key>textureRect</key>
			<string>{{0,170},{116,83}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
		<key>geode.custom-keybinds/controllerBtn_RT_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{1,0}</string>
			<key>spriteSize</key>
			<string>{116,83}</string>
			<key>spriteSourceSize</key>
			<string>{118,84}</string>
			<key>textureRect</key>
			<string>{{0,255},{116,83}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
	</dict>
	<key>metadata</key>
	<dict>
		<key>format</key>
		<integer>3</integer>
		<key>realTextureFileName</key>
		<string>geode.custom-keybinds/KB_ControllerSheet-uhd.png</string>
		<key>size</key>
		<string>{116,338}</string>
		<key>textureFileName</key>
		<string>geode.custom-keybinds/KB_ControllerSheet-uhd.png</string>
	</dict>
</dict>
</plist>