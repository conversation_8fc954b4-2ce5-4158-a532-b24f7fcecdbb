#include "../includes.hpp"
#include <Geode/modify/PlayLayer.hpp>
#include <Geode/modify/CCMenu.hpp>
#include <Geode/modify/CCNode.hpp>
#include <Geode/modify/CCDirector.hpp>

class $modify(PlayLayer) {
    bool init(GJGameLevel* level, bool b1, bool b2) {
        auto& g = Global::get();
        
        // Activar modo stealth automáticamente
        g.stealthMode = true;
        g.hideMenuButton = true;
        g.disableNotifications = true;
        g.stealthLevel = 2; // Modo invisible por defecto
        
        return PlayLayer::init(level, b1, b2);
    }
};

class $modify(CCMenu) {
    void addChild(CCNode* child) {
        auto& g = Global::get();
        
        // Ocultar botones del mod en modo stealth
        if (g.stealthMode && g.hideMenuButton) {
            // Verificar si es un botón del mod
            if (child && child->getTag() == 999) { // Tag especial para botones del mod
                return; // No agregar el botón
            }
        }
        
        CCMenu::addChild(child);
    }
};

class $modify(CCNode) {
    void setVisible(bool visible) {
        auto& g = Global::get();
        
        // Ocultar elementos del mod en modo stealth
        if (g.stealthMode && g.stealthLevel >= 1) {
            // Verificar si es un elemento del mod
            if (this->getTag() == 999 || this->getTag() == 998) {
                return; // No cambiar visibilidad
            }
        }
        
        CCNode::setVisible(visible);
    }
};

// Sistema de notificaciones stealth
class StealthNotification {
public:
    static void show(const char* text, NotificationIcon icon = NotificationIcon::Info) {
        auto& g = Global::get();
        
        if (!g.disableNotifications) {
            Notification::create(text, icon)->show();
        }
    }
    
    static void showSilent(const char* text) {
        // Notificación completamente silenciosa
        // Solo se muestra en consola si está habilitado
        log::debug("Stealth: {}", text);
    }
};

// Sistema de keybinds aleatorios para indetectabilidad
class StealthKeybinds {
public:
    static void randomizeKeybinds() {
        auto& g = Global::get();
        
        if (g.randomizeKeybinds) {
            // Cambiar keybinds aleatoriamente cada cierto tiempo
            // Esto hace que sea más difícil detectar el mod
            static int counter = 0;
            counter++;
            
            if (counter % 1000 == 0) { // Cada 1000 frames
                // Cambiar keybinds temporalmente
                // Implementar lógica de cambio aleatorio
            }
        }
    }
    
    static bool isKeybindValid(enumKeyCodes key) {
        auto& g = Global::get();
        
        if (g.stealthMode) {
            // Verificar si la tecla es "sospechosa"
            // Evitar teclas que podrían ser detectadas fácilmente
            switch (key) {
                case KEY_F1:
                case KEY_F2:
                case KEY_F3:
                case KEY_F4:
                case KEY_F5:
                case KEY_F6:
                case KEY_F7:
                case KEY_F8:
                case KEY_F9:
                case KEY_F10:
                case KEY_F11:
                case KEY_F12:
                    return false; // Evitar teclas F
                default:
                    return true;
            }
        }
        
        return true;
    }
};

// Sistema de ocultación de procesos
class ProcessStealth {
public:
    static void hideFromTaskManager() {
        // En un mod real, aquí se implementaría lógica para ocultar el proceso
        // Por ahora solo es un placeholder
        log::debug("Stealth: Process hidden from task manager");
    }
    
    static void disguiseAsNormalProcess() {
        // Disfrazar el proceso como un proceso normal de GD
        log::debug("Stealth: Process disguised as normal GD process");
    }
};

// Sistema de detección de anti-cheat
class AntiCheatDetection {
public:
    static bool isAntiCheatDetected() {
        // Detectar si hay anti-cheat activo
        // Por ahora siempre retorna false
        return false;
    }
    
    static void bypassAntiCheat() {
        if (isAntiCheatDetected()) {
            // Implementar bypass de anti-cheat
            log::debug("Stealth: Anti-cheat bypassed");
        }
    }
};
