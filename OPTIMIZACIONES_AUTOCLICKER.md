# Optimizaciones del Autoclicker - Eliminación de Lag

## Problemas Identificados

### 1. **Procesamiento Múltiple por Frame**
- El autoclicker se ejecutaba múltiples veces por frame
- Causaba sobrecarga innecesaria del CPU

### 2. **Llamadas Recursivas a handleButton**
- El autoclicker llamaba a `handleButton` de forma recursiva
- Creaba bucles infinitos y lag

### 3. **Falta de Límites de Rendimiento**
- No había límites para las acciones por frame
- Podía consumir demasiados recursos

### 4. **Conflicto con TPS Bypass**
- El TPS bypass interfería con el autoclicker
- Causaba sincronización incorrecta

## Soluciones Implementadas

### 1. **Prevención de Procesamiento Múltiple**
```cpp
int lastProcessFrame = -1; // Prevenir procesamiento múltiple por frame
int currentFrame = Global::getCurrentFrame();
if (currentFrame == m_fields->lastProcessFrame) return;
m_fields->lastProcessFrame = currentFrame;
```

### 2. **Límites de Seguridad**
```cpp
int processedActions = 0;
const int MAX_ACTIONS_PER_FRAME = 4;

for (int i = 0; i < 2 && processedActions < MAX_ACTIONS_PER_FRAME; i++) {
    // Verificar límites de seguridad
    if (holdFor[i] <= 0 || releaseFor[i] <= 0) continue;
    // ...
    processedActions++;
}
```

### 3. **Optimización de Rendimiento**
```cpp
// Optimización: procesar solo cada 2 frames si hay lag
m_fields->performanceCounter++;
if (m_fields->performanceCounter % 2 == 0 && g.tpsEnabled) return;
```

### 4. **Prevención de Llamadas Recursivas**
```cpp
// Prevenir llamadas recursivas del autoclicker
if (f->autoclick[i] || (realPlayer1 && !g.autoclickerP1) || (!realPlayer1 && !g.autoclickerP2)) {
    return GJBaseGameLayer::handleButton(hold, button, player1);
}
```

### 5. **Optimización del TPS Bypass**
```cpp
// Límite máximo de iteraciones para evitar lag
const int MAX_ITERATIONS = 10;
if (mult > MAX_ITERATIONS) mult = MAX_ITERATIONS;

// Límite de tiempo más estricto para evitar lag
if (std::chrono::high_resolution_clock::now() - startTime > std::chrono::duration<double, std::milli>(8.333f)) {
    mult = i + 1;
    break;
}
```

### 6. **Función de Limpieza**
```cpp
void cleanupAutoclicker() {
    auto& f = m_fields;
    for (int i = 0; i < 2; i++) {
        f->framesHolding[i] = 0;
        f->framesReleasing[i] = 0;
        f->autoclick[i] = false;
        f->holding[i] = false;
    }
    f->lastProcessFrame = -1;
    f->performanceCounter = 0;
}
```

## Resultados Esperados

1. **Eliminación del Lag**: El autoclicker ya no debería causar lag significativo
2. **Mejor Sincronización**: Los clicks deberían ser más precisos y consistentes
3. **Menor Uso de CPU**: El procesamiento está optimizado para usar menos recursos
4. **Compatibilidad Mejorada**: El autoclicker funciona mejor con otros mods

## Configuración Recomendada

- **holdFor**: 1-5 frames (dependiendo del nivel)
- **releaseFor**: 1-3 frames (dependiendo del nivel)
- **TPS**: 240 para máxima precisión
- **Autoclicker P1/P2**: Activar solo el necesario

## Notas Importantes

- El autoclicker ahora tiene límites de seguridad para evitar bucles infinitos
- Se procesa solo cuando es necesario para optimizar rendimiento
- La función de limpieza ayuda a mantener el estado consistente
- Los conflictos con TPS bypass han sido resueltos 