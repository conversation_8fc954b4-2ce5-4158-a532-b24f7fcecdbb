
#include "includes.hpp"
#include "ui/game_ui.hpp"

#include <Geode/modify/CCKeyboardDispatcher.hpp>
#include <Geode/modify/CCTouchDispatcher.hpp>

#ifdef GEODE_IS_WINDOWS

#include <geode.custom-keybinds/include/Keybinds.hpp>

#endif

const std::vector<std::string> keybindIDs = {};

class $modify(CCKeyboardDispatcher) {
  bool dispatchKeyboardMSG(enumKeyCodes key, bool isKeyDown, bool isKeyRepeat) {
  
    auto& g = Global::get();

    int keyInt = static_cast<int>(key);
    if (g.allKeybinds.contains(keyInt) && !isKeyRepeat) {
      for (size_t i = 0; i < 6; i++) {
        if (std::find(g.keybinds[i].begin(), g.keybinds[i].end(), keyInt) != g.keybinds[i].end())
          g.heldButtons[i] = isKeyDown;
      }
    }

    // Sistema de keybinds mejorado e indetectable
    // Solo procesar si no está en pausa y no hay menús abiertos
    if (!isKeyRepeat && PlayLayer::get() && !PlayLayer::get()->m_isPaused) {
        
        // Verificar si stealth keybinds están habilitados
        bool stealthKeybindsEnabled = g.mod->getSettingValue<bool>("stealth_keybinds");
        
        if (stealthKeybindsEnabled) {
            // T - Grabar macro (indetectable)
            if (key == KEY_T && isKeyDown) {
                if (g.state == state::recording)
                    Macro::stopRecording();
                else
                    Macro::startRecording();

                // Retornar false para evitar que se procese más
                return false;
            }

            // Y - Reproducir macro (indetectable)
            if (key == KEY_Y && isKeyDown) {
                if (g.state == state::playing)
                    Macro::stopPlaying();
                else
                    Macro::startPlaying();

                return false;
            }

            // Q - Reset completo del sistema (indetectable)
            if (key == KEY_Q && isKeyDown) {
                // Reset completo del sistema de macros con manejo seguro de estados
                if (g.state == state::recording)
                    Macro::stopRecording(false);

                if (g.state == state::playing)
                    Macro::stopPlaying();

                g.state = state::none;
                g.currentAction = 0;
                g.currentFrameFix = 0;
                g.restart = false;
                g.macro.inputs.clear();
                g.macro.frameFixes.clear();

                PlayLayer* pl = PlayLayer::get();
                if (pl) {
                    pl->m_player1->releaseAllButtons();
                    pl->m_player2->releaseAllButtons();
                }

                Interface::updateLabels();
                Interface::updateButtons();

                return false;
            }

            // Resto de teclas stealth deshabilitadas para evitar conflictos
        }
    }

    return CCKeyboardDispatcher::dispatchKeyboardMSG(key, isKeyDown, isKeyRepeat);
  }
};

#ifdef GEODE_IS_ANDROID

namespace keybinds {

  struct ActionID {};

};

#endif

using namespace keybinds;

void onKeybind(bool down, ActionID id) {
#ifdef GEODE_IS_WINDOWS
  (void)down;
  (void)id;
#endif
}

$execute{

  #ifdef GEODE_IS_WINDOWS

    // No se registran keybinds adicionales para evitar conflictos con el sistema de macros.

    for (int i = 0; i < keybindIDs.size(); i++) {
        new EventListener([=](InvokeBindEvent* event) { onKeybind(event->isDown(), event->getID()); return ListenerResult::Propagate;
        }, InvokeBindFilter(nullptr, (""_spr) + keybindIDs[i]));
    }

  #endif
}
