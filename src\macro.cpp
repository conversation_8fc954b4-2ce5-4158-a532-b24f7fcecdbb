#include "includes.hpp"
#include "ui/record_layer.hpp"
#include "ui/game_ui.hpp"

#include <Geode/modify/PlayLayer.hpp>

#include <algorithm>
#include <chrono>
#include <cctype>
#include <ctime>
#include <fstream>
#include <functional>
#include <random>
#include <filesystem>

namespace {

std::string sanitizeFilename(const std::string& value) {
    std::string sanitized;
    sanitized.reserve(value.size());

    bool previousUnderscore = false;
    for (char ch : value) {
        unsigned char c = static_cast<unsigned char>(ch);
        char toAppend;
        if (std::isalnum(c)) {
            toAppend = ch;
            previousUnderscore = false;
        }
        else if (ch == '-' || ch == '_') {
            toAppend = '_';
        }
        else if (std::isspace(c)) {
            toAppend = '_';
        }
        else {
            toAppend = '_';
        }

        if (toAppend == '_') {
            if (!previousUnderscore) {
                sanitized.push_back('_');
                previousUnderscore = true;
            }
        }
        else {
            sanitized.push_back(toAppend);
            previousUnderscore = false;
        }
    }

    while (!sanitized.empty() && sanitized.front() == '_')
        sanitized.erase(sanitized.begin());
    while (!sanitized.empty() && sanitized.back() == '_')
        sanitized.pop_back();

    if (sanitized.empty())
        return "level";

    return sanitized;
}

std::string timestampString() {
    auto now = std::chrono::system_clock::now();
    std::time_t raw = std::chrono::system_clock::to_time_t(now);
    std::tm timeinfo{};
#ifdef GEODE_IS_WINDOWS
    localtime_s(&timeinfo, &raw);
#else
    localtime_r(&raw, &timeinfo);
#endif
    char buffer[32];
    if (std::strftime(buffer, sizeof(buffer), "%Y%m%d_%H%M%S", &timeinfo) == 0)
        return "time";
    return buffer;
}

std::string levelKeyFor(GJGameLevel* level) {
    if (!level) return "";

    int id = level->m_levelID.value();
    if (id > 0)
        return fmt::format("id:{}", id);

    std::string sanitizedName = sanitizeFilename(level->m_levelName);
    size_t hashValue = std::hash<std::string>{}(level->m_levelName);
    return fmt::format("custom:{}:{:016x}", sanitizedName, static_cast<unsigned long long>(hashValue));
}

std::string levelDirectoryName(GJGameLevel* level) {
    if (!level) return "level_unknown";

    int id = level->m_levelID.value();
    if (id > 0)
        return fmt::format("level_{}", id);

    size_t hashValue = std::hash<std::string>{}(level->m_levelName);
    return fmt::format("level_custom_{:016x}", static_cast<unsigned long long>(hashValue));
}

std::string levelFilePrefix(GJGameLevel* level) {
    if (!level) return "level";

    int id = level->m_levelID.value();
    if (id > 0)
        return fmt::format("id{}", id);

    size_t hashValue = std::hash<std::string>{}(level->m_levelName);
    return fmt::format("custom{:016x}", static_cast<unsigned long long>(hashValue));
}

std::filesystem::path levelDirectoryPath(GJGameLevel* level) {
    std::filesystem::path base = Mod::get()->getSettingValue<std::filesystem::path>("macros_folder");
    return base / levelDirectoryName(level);
}

bool ensureDirectory(const std::filesystem::path& path) {
    std::error_code ec;
    if (std::filesystem::exists(path, ec))
        return true;

    std::filesystem::create_directories(path, ec);
    if (ec) {
        log::warn("Failed to create directory {} ({})", path.string(), ec.message());
        return false;
    }

    return true;
}

bool samePath(const std::filesystem::path& a, const std::filesystem::path& b) {
    if (a.empty() || b.empty())
        return false;

#ifdef GEODE_IS_WINDOWS
    return Utils::toLower(a.lexically_normal().string()) == Utils::toLower(b.lexically_normal().string());
#else
    return a.lexically_normal() == b.lexically_normal();
#endif
}

bool hasMacroExtension(const std::filesystem::path& path) {
    std::string filename = Utils::toLower(path.filename().string());
    if (filename.size() >= 9 && filename.substr(filename.size() - 9) == ".gdr.json")
        return true;

    std::string extension = Utils::toLower(path.extension().string());
    return extension == ".gdr" || extension == ".json";
}

std::vector<std::filesystem::path> collectLevelMacros(const std::filesystem::path& directory) {
    std::vector<std::filesystem::path> macros;
    std::error_code ec;

    if (!std::filesystem::exists(directory, ec))
        return macros;

    for (auto const& entry : std::filesystem::directory_iterator(directory, ec)) {
        if (ec)
            break;

        if (!entry.is_regular_file())
            continue;

        if (!hasMacroExtension(entry.path()))
            continue;

        macros.push_back(entry.path());
    }

    std::sort(macros.begin(), macros.end());
    return macros;
}

} // namespace
void Macro::recordAction(int frame, int button, bool player2, bool hold) {
    PlayLayer* pl = PlayLayer::get();
    if (!pl) return;

    auto& g = Global::get();

    if (g.macro.inputs.empty())
        Macro::updateInfo(pl);

    if (g.tpsEnabled) g.macro.framerate = g.tps;

    if (Macro::flipControls())
      player2 = !player2;

    g.macro.inputs.push_back(input(frame, button, player2, hold));
}

void Macro::recordFrameFix(int frame, PlayerObject* p1, PlayerObject* p2) {
    float p1Rotation = p1->getRotation();
    float p2Rotation = p2->getRotation();

    while (p1Rotation < 0 || p1Rotation > 360)
      p1Rotation += p1Rotation < 0 ? 360.f : -360.f;
    
    while (p2Rotation < 0 || p2Rotation > 360)
      p2Rotation += p2Rotation < 0 ? 360.f : -360.f;

    Global::get().macro.frameFixes.push_back({
      frame,
      { p1->getPosition(), p1Rotation },
      { p2->getPosition(), p2Rotation }
    });
}

bool Macro::flipControls() {
    PlayLayer* pl = PlayLayer::get();
    if (!pl) return GameManager::get()->getGameVariable("0010");

    return pl->m_levelSettings->m_platformerMode ? false : GameManager::get()->getGameVariable("0010");
}

void Macro::autoSave(GJGameLevel* level, int number) {
    if (!level) level = PlayLayer::get() != nullptr ? PlayLayer::get()->m_level : nullptr;
    if (!level) return;

    std::string levelname = level->m_levelName;
    std::string sanitizedLevelName = sanitizeFilename(levelname);
    std::filesystem::path autoSavesPath = Mod::get()->getSettingValue<std::filesystem::path>("autosaves_folder");
    std::filesystem::path path = autoSavesPath / fmt::format("autosave_{}_{}", sanitizedLevelName, number);

    if (!std::filesystem::exists(autoSavesPath)) return;

    std::string username = GJAccountManager::sharedState() != nullptr ? GJAccountManager::sharedState()->m_username : "";
    int result = Macro::save(username, fmt::format("AutoSave {} in level {}", number, levelname), path.string());

    if (result != 0)
        log::debug("Failed to autosave macro. ID: {}. Path: {}", result, path);
}

void Macro::tryAutosave(GJGameLevel* level, CheckpointObject* cp) {
    auto& g = Global::get();

    if (g.state != state::recording) return;
    if (!g.autosaveEnabled) return;
    if (!g.checkpoints.contains(cp)) return;
    if (g.checkpoints[cp].frame < g.lastAutoSaveFrame) return;

    std::filesystem::path autoSavesPath = g.mod->getSettingValue<std::filesystem::path>("autosaves_folder");

    if (!std::filesystem::exists(autoSavesPath))
        return log::debug("Failed to access auto saves path.");

    std::string levelname = level->m_levelName;
    std::string sanitizedLevelName = sanitizeFilename(levelname);
    std::filesystem::path path = autoSavesPath / fmt::format("autosave_{}_{}", sanitizedLevelName, g.currentSession);
    std::error_code ec;
    std::filesystem::remove(path.string() + ".gdr", ec); // Remove previous save
    if (ec) log::warn("Failed to remove previous autosave");

    autoSave(level, g.currentSession);

}

void Macro::updateInfo(PlayLayer* pl) {
    if (!pl) return;

    auto& g = Global::get();

    GJGameLevel* level = pl->m_level;
    if (level->m_lowDetailModeToggled != g.macro.ldm)
        g.macro.ldm = level->m_lowDetailModeToggled;

    int id = level->m_levelID.value();
    if (id != g.macro.levelInfo.id)
        g.macro.levelInfo.id = id;

    std::string name = level->m_levelName;
    if (name != g.macro.levelInfo.name)
        g.macro.levelInfo.name = name;

    std::string author = GJAccountManager::sharedState()->m_username;
    if (g.macro.author != author)
        g.macro.author = author;

    if (g.macro.author == "")
        g.macro.author = "N/A";

    g.macro.botInfo.name = "xdBot";
    g.macro.botInfo.version = xdBotVersion;
    g.macro.xdBotMacro = true;
}

void Macro::updateTPS() {
    auto& g = Global::get();

    if (g.state != state::none && !g.macro.inputs.empty()) {
        g.previousTpsEnabled = g.tpsEnabled;
        g.previousTps = g.tps;

        g.tpsEnabled = g.macro.framerate != 240.f;
        if (g.tpsEnabled) g.tps = g.macro.framerate;

        g.mod->setSavedValue("macro_tps", g.tps);
        g.mod->setSavedValue("macro_tps_enabled", g.tpsEnabled);
        
    }
    else if (g.previousTps != 0.f) {
        g.tpsEnabled = g.previousTpsEnabled;
        g.tps = g.previousTps;
        g.previousTps = 0.f;
        g.mod->setSavedValue("macro_tps", g.tps);
        g.mod->setSavedValue("macro_tps_enabled", g.tpsEnabled);
    }

    if (g.layer) static_cast<RecordLayer*>(g.layer)->updateTPS();
}

void Macro::simplifyInputs() {
    auto& g = Global::get();
    auto& inputs = g.macro.inputs;

    if (inputs.empty())
        return;

    std::stable_sort(inputs.begin(), inputs.end(), [](const input& lhs, const input& rhs) {
        return lhs.frame < rhs.frame;
    });

    inputs.erase(std::unique(inputs.begin(), inputs.end(), [](const input& lhs, const input& rhs) {
        return lhs.frame == rhs.frame &&
               lhs.button == rhs.button &&
               lhs.player2 == rhs.player2 &&
               lhs.down == rhs.down;
    }), inputs.end());
}

std::filesystem::path Macro::saveForLevel(GJGameLevel* level) {
    auto& g = Global::get();

    if (g.macro.inputs.empty())
        return {};

    if (!level) {
        if (PlayLayer* pl = PlayLayer::get())
            level = pl->m_level;
    }

    if (!level)
        return {};

    std::filesystem::path directory = levelDirectoryPath(level);
    if (!ensureDirectory(directory))
        return {};

    if (PlayLayer* pl = PlayLayer::get())
        Macro::updateInfo(pl);

    std::string sanitizedName = sanitizeFilename(level->m_levelName);
    std::string prefix = levelFilePrefix(level);
    std::string stamp = timestampString();

    std::string candidatePath = (directory / fmt::format("{}_{}_{}", prefix, sanitizedName, stamp)).string();
    std::string extension = ".gdr";
    int iterations = 0;

    while (std::filesystem::exists(candidatePath + extension)) {
        iterations++;

        if (iterations > 1) {
            int length = 3 + std::to_string(iterations - 1).length();
            candidatePath.erase(candidatePath.length() - length, length);
        }

        candidatePath += fmt::format(" ({})", iterations);
    }

    std::string author = g.macro.author;
    if (author.empty())
        author = GJAccountManager::sharedState() != nullptr ? GJAccountManager::sharedState()->m_username : "N/A";

    std::string description = g.macro.description;
    if (description.empty())
        description = fmt::format("Auto macro {}", stamp);

    int result = Macro::save(author, description, candidatePath);
    if (result != 0) {
        log::warn("Failed to auto-save macro for level {} ({}) [error {}]", level->m_levelName, levelKeyFor(level), result);
        return {};
    }

    std::filesystem::path finalPath = candidatePath + extension;

    g.autoLoadedMacroPool = collectLevelMacros(directory);
    g.autoLoadedMacroPath = finalPath;
    g.autoLoadedLevelKey = levelKeyFor(level);

    return finalPath;
}

int Macro::save(std::string author, std::string desc, std::string path, bool json) {
    auto& g = Global::get();

    if (g.macro.inputs.empty()) return 31;

    Macro::simplifyInputs();

    std::string extension = json ? ".gdr.json" : ".gdr";

    int iterations = 0;

    while (std::filesystem::exists(path + extension)) {
        iterations++;

        if (iterations > 1) {
            int length = 3 + std::to_string(iterations - 1).length();
            path.erase(path.length() - length, length);
        }

        path += fmt::format(" ({})", std::to_string(iterations));
    }

    path += extension;

    // Log eliminado para mejor rendimiento

    g.macro.author = author;
    g.macro.description = desc;
    g.macro.duration = g.macro.inputs.back().frame / g.macro.framerate;

    std::wstring widePath = Utils::widen(path);

    if (widePath == L"Widen Error")
        return 30;

    std::ofstream f(widePath, std::ios::binary);

    if (!f)
        f.open(path, std::ios::binary);

    if (!f)
        return 20;

    std::vector<gdr::FrameFix> frameFixes = g.macro.frameFixes;

    auto data = g.macro.exportData(json);

    f.write(reinterpret_cast<const char*>(data.data()), data.size());

    if (!f) {
        f.close();
        return 21;
    }

    if (!f)
        return 22;

    f.close();

    return 0;
}

bool Macro::loadFromFile(const std::filesystem::path& path) {
    if (path.empty())
        return false;

    auto& g = Global::get();

    Macro newMacro;
    std::string extension = Utils::toLower(path.extension().string());

    if (extension == ".xd") {
        if (!Macro::loadXDFile(path))
            return false;

        newMacro = g.macro;
    }
    else {
        std::ifstream file(path, std::ios::binary);
        if (!file.is_open()) {
            log::warn("Failed to open macro file {}", path.string());
            return false;
        }

        file.seekg(0, std::ios::end);
        std::streamoff size = file.tellg();
        if (size <= 0) {
            log::warn("Macro file {} is empty", path.string());
            return false;
        }

        file.seekg(0, std::ios::beg);

        std::vector<std::uint8_t> data(static_cast<size_t>(size));
        file.read(reinterpret_cast<char*>(data.data()), size);
        if (!file) {
            log::warn("Failed to read macro file {}", path.string());
            return false;
        }

        file.close();

        newMacro = Macro::importData(data);
    }

    g.macro = newMacro;
    g.currentAction = 0;
    g.currentFrameFix = 0;
    g.restart = true;
    g.macro.canChangeFPS = false;
    g.macro.xdBotMacro = isXdBotMacroName(g.macro.botInfo.name);

    Macro::updateTPS();

    return true;
}

bool Macro::loadRandomForLevel(GJGameLevel* level, bool forceDifferent) {
    auto& g = Global::get();

    if (!level) {
        if (PlayLayer* pl = PlayLayer::get())
            level = pl->m_level;
    }

    if (!level)
        return false;

    std::filesystem::path directory = levelDirectoryPath(level);
    std::vector<std::filesystem::path> macros = collectLevelMacros(directory);

    g.autoLoadedMacroPool = macros;
    g.autoLoadedLevelKey = levelKeyFor(level);

    if (macros.empty()) {
        if (g.macroLoadedAutomatically) {
            g.macro.inputs.clear();
            g.macro.frameFixes.clear();
            g.macroLoadedAutomatically = false;
        }

        g.autoLoadedMacroPath.clear();
        return false;
    }

    std::filesystem::path currentPath = g.autoLoadedMacroPath;

    if (!forceDifferent && g.macroLoadedAutomatically && !currentPath.empty()) {
        auto it = std::find_if(macros.begin(), macros.end(), [&](const std::filesystem::path& candidate) {
            return samePath(candidate, currentPath);
        });

        if (it != macros.end()) {
            if (Macro::loadFromFile(*it)) {
                g.autoLoadedMacroPath = *it;
                g.macroLoadedAutomatically = true;
                g.macroJustRecorded = false;
                return true;
            }
        }
    }

    std::vector<std::filesystem::path> candidates = macros;

    if (forceDifferent && candidates.size() > 1) {
        candidates.erase(std::remove_if(candidates.begin(), candidates.end(), [&](const std::filesystem::path& candidate) {
            return samePath(candidate, currentPath);
        }), candidates.end());

        if (candidates.empty())
            candidates = macros;
    }

    std::mt19937 rng(static_cast<uint32_t>(std::chrono::steady_clock::now().time_since_epoch().count()));
    std::shuffle(candidates.begin(), candidates.end(), rng);

    for (const auto& candidate : candidates) {
        if (!forceDifferent && samePath(candidate, currentPath) && g.macroLoadedAutomatically)
            continue;

        if (Macro::loadFromFile(candidate)) {
            g.autoLoadedMacroPath = candidate;
            g.macroLoadedAutomatically = true;
            g.macroJustRecorded = false;
            return true;
        }
    }

    g.macroLoadedAutomatically = false;
    g.autoLoadedMacroPath.clear();
    return false;
}

bool Macro::loadXDFile(std::filesystem::path path) {

    Macro newMacro = Macro::XDtoGDR(path);
    if (newMacro.description == "fail")
        return false;

    Global::get().macro = newMacro;
    return true;
}

Macro Macro::XDtoGDR(std::filesystem::path path) {

    Macro newMacro;
    newMacro.author = "N/A";
    newMacro.description = "N/A";
    newMacro.gameVersion = GEODE_GD_VERSION;

    std::ifstream file(Utils::widen(path.string()));
    std::string line;

    if (!file.is_open()) {
        newMacro.description = "fail";
        return newMacro;
    }

    bool firstIt = true;
    bool andr = false;

    float fpsMultiplier = 1.f;

    while (std::getline(file, line)) {
        std::string item;
        std::stringstream ss(line);
        std::vector<std::string> action;

        while (std::getline(ss, item, '|'))
            action.push_back(item);

        if (action.size() < 4) {
            if (action[0] == "android")
                fpsMultiplier = 4.f;
            else {
                int fps = std::stoi(action[0]);
                fpsMultiplier = 240.f / fps;
            }

            continue;
        }

        int frame = static_cast<int>(round(std::stoi(action[0]) * fpsMultiplier));
        int button = std::stoi(action[2]);
        bool hold = action[1] == "1";
        bool player2 = action[3] == "1";
        bool posOnly = action[4] == "1";

        if (!posOnly)
            newMacro.inputs.push_back(input(frame, button, player2, hold));
        else {
            cocos2d::CCPoint p1Pos = ccp(std::stof(action[5]), std::stof(action[6]));
            cocos2d::CCPoint p2Pos = ccp(std::stof(action[11]), std::stof(action[12]));

            newMacro.frameFixes.push_back({ frame, {p1Pos, 0.f, false}, {p2Pos, 0.f, false} });
        }
    }

    file.close();

    return newMacro;

}

void Macro::resetVariables() {
    auto& g = Global::get();

    g.ignoreFrame = -1;
    g.ignoreJumpButton = -1;

    g.delayedFrameReleaseMain[0] = -1;
    g.delayedFrameReleaseMain[1] = -1;

    g.delayedFrameInput[0] = -1;
    g.delayedFrameInput[1] = -1;

    g.addSideHoldingMembers[0] = false;
    g.addSideHoldingMembers[1] = false;
    for (int x = 0; x < 2; x++) {
        for (int y = 0; y < 2; y++)
            g.delayedFrameRelease[x][y] = -1;
    }
}

void Macro::resetState(bool cp) {
    auto& g = Global::get();

    g.restart = false;
    g.state = state::none;

    if (!cp)
        g.checkpoints.clear();

    Interface::updateLabels();
    Interface::updateButtons();

    Macro::resetVariables();
}

void Macro::togglePlaying() {
    auto& g = Global::get();

    if (g.state == state::playing)
        Macro::stopPlaying();
    else
        Macro::startPlaying();
}

void Macro::toggleRecording() {
    auto& g = Global::get();

    if (g.state == state::recording)
        Macro::stopRecording();
    else
        Macro::startRecording();
}

void Macro::startRecording() {
    if (Global::hasIncompatibleMods()) return;

    auto& g = Global::get();

    if (g.state == state::recording)
        return;

    if (g.state == state::playing)
        Macro::stopPlaying();

    g.state = state::recording;
    g.currentAction = 0;
    g.currentFrameFix = 0;
    g.restart = true;

    g.macroLoadedAutomatically = false;
    g.macroJustRecorded = false;

    if (PlayLayer* pl = PlayLayer::get()) {
        if (!pl->m_isPaused)
            pl->pauseGame(false);
    }

    Interface::updateLabels();
    Interface::updateButtons();
    Macro::updateTPS();

    g.lastAutoSaveMS = std::chrono::steady_clock::now();
}

void Macro::stopRecording(bool save) {
    auto& g = Global::get();

    if (g.state != state::recording)
        return;

    g.state = state::none;
    g.restart = false;

    if (save) {
        std::filesystem::path savedPath = Macro::saveForLevel();

        if (!savedPath.empty()) {
            g.macroJustRecorded = true;
            g.macroLoadedAutomatically = false;
        }
    }

    Interface::updateLabels();
    Interface::updateButtons();
    Macro::updateTPS();

    g.lastAutoSaveMS = std::chrono::steady_clock::now();
}

void Macro::startPlaying() {
    if (Global::hasIncompatibleMods()) return;

    auto& g = Global::get();

    if (g.state == state::playing)
        return;

    if (g.state == state::recording)
        Macro::stopRecording();

    if (PlayLayer* pl = PlayLayer::get()) {
        GJGameLevel* level = pl->m_level;

        if (g.macroJustRecorded) {
            g.macroJustRecorded = false;
        }
        else {
            bool sameLevelAuto = g.macroLoadedAutomatically && g.autoLoadedLevelKey == levelKeyFor(level);

            if (!sameLevelAuto || g.macro.inputs.empty())
                Macro::loadRandomForLevel(level, false);
            else
                Macro::loadRandomForLevel(level, g.autoLoadedMacroPool.size() > 1);
        }
    }

    if (g.macro.inputs.empty())
        return;

    g.state = state::playing;
    g.restart = false;
    g.macro.xdBotMacro = isXdBotMacroName(g.macro.botInfo.name);

    if (PlayLayer* pl = PlayLayer::get()) {
        int currentFrame = Global::getCurrentFrame();

        g.currentAction = 0;
        while (g.currentAction < g.macro.inputs.size() && g.macro.inputs[g.currentAction].frame <= currentFrame)
            g.currentAction++;

        g.currentFrameFix = 0;

        if (!pl->m_isPaused && !pl->m_levelEndAnimationStarted)
            pl->pauseGame(false);
    }

    Interface::updateLabels();
    Interface::updateButtons();
    Macro::updateTPS();
}

void Macro::stopPlaying(bool releaseButtons) {
    auto& g = Global::get();

    if (g.state != state::playing)
        return;

    g.state = state::none;
    g.restart = false;

    if (releaseButtons) {
        if (PlayLayer* pl = PlayLayer::get()) {
            pl->m_player1->releaseAllButtons();
            pl->m_player2->releaseAllButtons();
        }
    }

    Interface::updateLabels();
    Interface::updateButtons();
    Macro::updateTPS();
}

bool Macro::shouldStep() {
    auto& g = Global::get();

    if (g.stepFrame) return true;
    if (Global::getCurrentFrame() == 0) return true;

    // if (g.ignoreFrame != -1) return true;
    // if (g.ignoreJumpButton != -1) return true;

    // if (g.delayedFrameReleaseMain[0] != -1) return true;
    // if (g.delayedFrameReleaseMain[1] != -1) return true;

    // if (g.delayedFrameInput[0] != -1) return true;
    // if (g.delayedFrameInput[1] != -1) return true;

    // for (int x = 0; x < 2; x++) {
    //     for (int y = 0; y < 2; y++) {
    //         if (g.delayedFrameRelease[x][y] != -1) return true;
    //     }
    // }

    return false;
}
