#pragma once
#include "../includes.hpp"

#include <Geode/binding/GJUserScore.hpp>
#include <Geode/modify/ProfilePage.hpp>

using namespace geode::prelude;

// -----------------------------------------------------------------------------
// Friend UI Shim — INACTIVO por defecto (no modifica nada)
// -----------------------------------------------------------------------------
// Propósito: dejar un hook listo que NUNCA afecte la UI a menos que tú lo
// habilites explícitamente. Ideal si tienes otro programa que controlará los
// sprites/botones de amigos y sólo quieres que este mod NO interfiera.
//
// Cómo funciona:
// - Compila este archivo. Por defecto, FRIEND_UI_SHIM = 0 → no hace nada.
// - Si alguna vez quisieras activarlo para pruebas, compila con -DFRIEND_UI_SHIM=1.
// - Además, requiere que Global::get().cosmeticAllFriends sea true para actuar.
// - Si defines -DSHIM_DRY_RUN=1, sólo registrará logs (sin tocar UI).
//
// Seguridad:
// - No toca red ni estado de cuenta.
// - No accede a miembros opcionales.
// - No crea ni elimina nodos si está inactivo.
// -----------------------------------------------------------------------------

#ifndef FRIEND_UI_SHIM
#define FRIEND_UI_SHIM 0
#endif

#ifndef SHIM_DRY_RUN
#define SHIM_DRY_RUN 0
#endif

namespace friend_ui_shim {
    static inline void logState(const char* where, GJUserScore* s) {
#if SHIM_DRY_RUN
        if (!s) { log::info("[FriendUIShim] {}: score=null", where); return; }
        log::info("[FriendUIShim] {}: accID={} status={} req={}",
                  where, s->m_accountID, s->m_friendStatus, s->m_friendReqStatus);
#endif
    }

#if FRIEND_UI_SHIM
    // Acción mínima: ocultar el botón de "Agregar amigo" si existiera.
    // NOTA: Ajusta criterios/IDs sólo si realmente necesitas activarlo.
    static inline void hideAddFriendIfPresent(CCNode* root) {
#if SHIM_DRY_RUN
        log::info("[FriendUIShim] hideAddFriendIfPresent: DRY_RUN (no-op)");
        return;
#else
        if (!root) return;
        if (auto* children = root->getChildren()) {
            CCObject* obj = nullptr;
            CCARRAY_FOREACH(children, obj) {
                auto* node = dynamic_cast<CCNode*>(obj);
                if (!node) continue;
                // Heurística conservadora para no romper UI ajena
                auto id = node->getID();
                if (!id.empty()) {
                    // match leve, sólo si claramente se llama "add friend"
                    if (id.find("add") != std::string::npos && id.find("friend") != std::string::npos) {
                        node->setVisible(false);
                    }
                }
            }
        }
#endif
    }
#endif // FRIEND_UI_SHIM
} // namespace friend_ui_shim

// Hook extremadamente conservador
class $modify(FriendUIShim_ProfilePage, ProfilePage) {
public:
    void loadPageFromUserInfo(GJUserScore* score) {
        friend_ui_shim::logState("BEFORE", score);

        // Llamada base SIEMPRE primero
        ProfilePage::loadPageFromUserInfo(score);

        // Salida si shim está desactivado o si no está el flag global
#if FRIEND_UI_SHIM
        auto& g = Global::get();
        if (!g.cosmeticAllFriends) return; // no activo → no tocar
        if (score && score->isCurrentUser()) return; // nunca tocar tu perfil
        friend_ui_shim::hideAddFriendIfPresent(this);
#else
        (void)score; // silenciar warning cuando está inactivo
#endif

        friend_ui_shim::logState("AFTER", score);
    }
};
