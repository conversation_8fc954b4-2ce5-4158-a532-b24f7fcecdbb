name: 'Verify bindings syntax'

on:
  pull_request:
    paths:
      - '.github/workflows/*'
      - 'bindings/2.2074/*'
      - 'bindings/include/**'
      - 'cmake/**'
      - 'codegen/**'
      - 'test/**'
      - 'CMakeLists.txt'
  push:
    branches:
    - '**'
    paths:
      - '.github/workflows/*'
      - 'bindings/2.2074/*'
      - 'bindings/include/**'
      - 'cmake/**'
      - 'codegen/**'
      - 'test/**'
      - 'CMakeLists.txt'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Restore cache
      id: codegen-cache
      uses: actions/cache@v4
      with:
        path: bin
        key: codegen-${{ hashFiles('codegen/**') }}

    - name: Build Codegen binary
      if: steps.codegen-cache.outputs.cache-hit != 'true'
      run: |
        cmake ./codegen -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=./bin -B build
        cmake --build build --config RelWithDebInfo
        cmake --install build

    - name: Upload Codegen binary
      uses: actions/upload-artifact@v4
      with:
        name: Codegen
        path: bin/Codegen

  verify:
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Download Codegen binary
      uses: actions/download-artifact@v4
      with:
        name: Codegen

    - name: Verify
      run: 'chmod +x ./Codegen && ./Codegen Win64 bindings/2.2074 out'

  test-members:
    # dont bother running member test if broma isnt even valid
    needs: verify

    strategy:
      fail-fast: false
      matrix:
        config:
        - name: Windows
          os: windows-2022
          prefixes: ''
          extra_flags: >
            -A x64
            -D USE_HACKY_SCRIPT=ON

        - name: Android32
          os: ubuntu-latest
          prefixes: ''
          extra_flags: >
            -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK_LATEST_HOME/build/cmake/android.toolchain.cmake
            -DANDROID_ABI=armeabi-v7a
            -DANDROID_PLATFORM=android-23
            -G Ninja
            -D USE_HACKY_SCRIPT=ON

        - name: Android64
          os: ubuntu-latest
          prefixes: ''
          extra_flags: >
            -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK_LATEST_HOME/build/cmake/android.toolchain.cmake
            -DANDROID_ABI=arm64-v8a
            -DANDROID_PLATFORM=android-23
            -G Ninja
            -D USE_HACKY_SCRIPT=ON

        - name: macOS (x64)
          os_identifier: mac
          os: macos-latest
          extra_flags: >
            -DCMAKE_C_COMPILER=clang
            -DCMAKE_CXX_COMPILER=clang++
            -DCMAKE_BUILD_TYPE=Debug
            -DCMAKE_OSX_ARCHITECTURES=x86_64
            -D USE_HACKY_SCRIPT=ON

        - name: macOS (ARM)
          os_identifier: mac-arm
          os: macos-latest
          extra_flags: >
            -DCMAKE_C_COMPILER=clang
            -DCMAKE_CXX_COMPILER=clang++
            -DCMAKE_BUILD_TYPE=Debug
            -DCMAKE_OSX_ARCHITECTURES=arm64
            -D USE_HACKY_SCRIPT=ON

        - name: iOS
          os_identifier: ios
          os: macos-latest
          extra_flags: >
            -DCMAKE_C_COMPILER=clang
            -DCMAKE_CXX_COMPILER=clang++
            -DCMAKE_BUILD_TYPE=Debug
            -DCMAKE_OSX_ARCHITECTURES=arm64
            -DCMAKE_OSX_DEPLOYMENT_TARGET=14.0
            -DCMAKE_SYSTEM_NAME=iOS
            -D USE_HACKY_SCRIPT=ON

    name: Test Offsets ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}

    steps:
    - name: Checkout bindings
      uses: actions/checkout@v4

    - name: Checkout geode
      uses: actions/checkout@v4
      with:
        repository: geode-sdk/geode
        # TODO: this is temporary
        ref: main
        path: geode

    - name: Set up codegen binary cache
      id: codegen-cache
      uses: actions/cache@v4
      with:
        path: build/bindings/codegen
        # cache by os since for android32 and 64 the codegen binary is the same
        key: codegen-${{ hashFiles('codegen/**') }}-${{ matrix.config.os }}

    # - name: Set up android env
    #   run: >
    #     sudo apt install ninja-build &&
    #     sudo add-apt-repository ppa:ubuntu-toolchain-r/test &&
    #     sudo apt-get update &&
    #     sudo apt-get install --only-upgrade libstdc++6
    #   if: matrix.config.os == 'ubuntu-latest'

    - name: Set GEODE_SDK
      shell: bash
      run: echo "GEODE_SDK=${{ github.workspace }}/geode" >> $GITHUB_ENV

    - name: Configure CMake
      shell: bash
      run: >
        cmake -B build -S test/members
        -D SKIP_BUILDING_CODEGEN=${{ steps.codegen-cache.outputs.cache-hit }}
        ${{ matrix.config.extra_flags }}
      # SKIP_BUILDING_CODEGEN will skip building codegen if finds a cached binary,
      # locally you dont need to set it
      # USE_HACKY_SCRIPT will use the python script and some hackery to print the
      # offsets in a nicer way. it is also not required

    - name: Build member test
      run: |
        cmake --build build --config RelWithDebInfo --parallel --target TestMembers

    - name: Show Errors
      run: python test/members/check.py build
