﻿name: Build Codegen

on:
  pull_request:
    paths:
      - '.github/workflows/build.yml'
      - 'codegen/**'
  push:
    branches:
    - '**'
    paths:
      - '.github/workflows/build.yml'
      - 'codegen/**'
      

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        config:
        - name: Windows
          os: windows-latest
          id: win
          extra_flags: ''
        - name: macOS
          os: macos-latest
          id: mac
          extra_flags: >
            -DCMAKE_OSX_ARCHITECTURES='x86_64;arm64'
            -DCMAKE_OSX_DEPLOYMENT_TARGET=10.15
            -DCMAKE_AR='/usr/bin/ar'
            -DCMAKE_RANLIB='/usr/bin/ranlib'
        - name: Linux
          os: ubuntu-22.04
          id: linux
          extra_flags: ''

    name: Build ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}

    env:
      CPM_SOURCE_CACHE: ${{ github.workspace }}/cpm-cache

    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Setup CPM Cache
      uses: actions/cache@v4
      with:
        path: cpm-cache
        key: cpm-${{ matrix.config.id }}-v1-${{ hashFiles('codegen/CMakeLists.txt') }}
        restore-keys: |
          cpm-${{ matrix.config.id }}-v1-

    - name: Install Ninja
      shell: bash
      run: |
        curl -L https://github.com/ninja-build/ninja/releases/latest/download/ninja-${{ matrix.config.id }}.zip -o ninja.zip
        7z x ninja.zip -o"$GITHUB_WORKSPACE/ninja"
        echo "$GITHUB_WORKSPACE/ninja" >> $GITHUB_PATH

    # - name: Update LLVM (Windows)
    #   shell: bash
    #   run: |
    #     curl -L https://github.com/llvm/llvm-project/releases/download/llvmorg-19.1.7/LLVM-19.1.7-win64.exe -o llvm-inst.exe
    #     7z x llvm-inst.exe -ollvm bin/clang.exe bin/clang++.exe bin/lld-link.exe bin/llvm-rc.exe bin/*.dll lib/clang/*/include/*
    #     echo "$GITHUB_WORKSPACE/llvm/bin" >> $GITHUB_PATH
    #   if: matrix.config.id == 'win'

    - name: Update Clang (Linux)
      shell: bash
      run: |
        sudo apt update
        sudo apt remove clang-14
        sudo apt install gcc-12 g++-12
        sudo ln -sf /usr/bin/gcc-12 /usr/bin/clang
        sudo ln -sf /usr/bin/g++-12 /usr/bin/clang++
      if: matrix.config.id == 'linux'

    - name: Configure
      run: >
        cmake codegen -B codegen/build
        -DCMAKE_BUILD_TYPE=Release
        -DCMAKE_C_COMPILER=clang
        -DCMAKE_CXX_COMPILER=clang++
        -G Ninja
        ${{ matrix.config.extra_flags }}

    - name: Build
      run: cmake --build codegen/build --config Release --parallel

    - name: Upload Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: geode-codegen-${{ matrix.config.id }}
        path: |
          codegen/build/Codegen
          codegen/build/Codegen.exe
        if-no-files-found: ignore

  publish:
    name: Publish
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Download Artifacts
      uses: actions/download-artifact@v4

    - name: Move Binaries
      run: |
        mv geode-codegen-win/Codegen.exe geode-codegen-win.exe
        mv geode-codegen-mac/Codegen geode-codegen-mac-temp
        mv geode-codegen-linux/Codegen geode-codegen-linux-temp
        rmdir geode-codegen-mac
        rmdir geode-codegen-linux
        mv geode-codegen-mac-temp geode-codegen-mac
        mv geode-codegen-linux-temp geode-codegen-linux

    - name: Update Codegen Release
      uses: andelf/nightly-release@main
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: codegen
        name: 'Codegen Release'
        body: Geode codegen release for commit ${{ github.sha }}.
        files: |
          ./geode-codegen-win.exe
          ./geode-codegen-mac
          ./geode-codegen-linux
