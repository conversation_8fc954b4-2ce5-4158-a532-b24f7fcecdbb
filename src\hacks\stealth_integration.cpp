#include "../includes.hpp"
#include <Geode/modify/CCMenu.hpp>
#include <Geode/modify/CCNode.hpp>

// Sistema de stealth integrado con el mod actual
class $modify(CCMenu) {
    void addChild(CCNode* child) {
        auto& g = Global::get();
        
        // Ocultar botones del mod si stealth está habilitado
        if (g.stealthMode && g.hideMenuButton) {
            // Verificar si es un botón del mod (tag especial)
            if (child && (child->getTag() == 999 || child->getTag() == 998)) {
                return; // No agregar el botón
            }
        }
        
        CCMenu::addChild(child);
    }
};

class $modify(CCNode) {
    void setVisible(bool visible) {
        auto& g = Global::get();
        
        // Ocultar elementos del mod si stealth está habilitado
        if (g.stealthMode && g.stealthLevel >= 1) {
            // Verificar si es un elemento del mod
            if (this->getTag() == 999 || this->getTag() == 998) {
                return; // No cambiar visibilidad
            }
        }
        
        CCNode::setVisible(visible);
    }
};

// Sistema de notificaciones stealth
class StealthNotification {
public:
    static void show(const char* text, NotificationIcon icon = NotificationIcon::Info) {
        auto& g = Global::get();
        
        if (!g.disableNotifications) {
            Notification::create(text, icon)->show();
        }
    }
    
    static void showSilent(const char* text) {
        // Notificación completamente silenciosa
        log::debug("Stealth: {}", text);
    }
};

// Función para activar stealth mode
void enableStealthMode() {
    auto& g = Global::get();
    
    if (g.stealthMode) {
        log::debug("Stealth: Stealth mode enabled");
    }
}

// Función para desactivar stealth mode
void disableStealthMode() {
    auto& g = Global::get();
    
    g.stealthMode = false;
    log::debug("Stealth: Stealth mode disabled");
} 