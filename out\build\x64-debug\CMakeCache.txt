# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug
# It was generated by CMake: C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Disables inclusion of the unversioned Enums header, allowing
// for builds on older Geode/Geometry Dash versions.
BINDINGS_VERSIONED_ONLY:BOOL=OFF

//Build libraries static
BUILD_SHARED_LIBS:BOOL=OFF

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Debug

//CXX compiler
CMAKE_CXX_COMPILER:STRING=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//C compiler
CMAKE_C_COMPILER:STRING=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//No help, variable specified on the command line.
CMAKE_INSTALL_PREFIX:PATH=C:/Users/<USER>/source/repos/Paibotgeode/out/install/x64-debug

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//make program
CMAKE_MAKE_PROGRAM:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x64/mt.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=xdBot2

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Program Files (x86)/Windows Kits/10/bin/10.0.26100.0/x64/rc.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

CMAKE_WARN_DEPRECATED:BOOL=ON

//Don't create a package lock file in the binary path
CPM_DONT_CREATE_PACKAGE_LOCK:BOOL=OFF

//Don't update the module path to allow using find_package
CPM_DONT_UPDATE_MODULE_PATH:BOOL=OFF

//Always download dependencies from source
CPM_DOWNLOAD_ALL:BOOL=OFF

//Add all packages added through CPM.cmake to the package lock
CPM_INCLUDE_ALL_IN_PACKAGE_LOCK:BOOL=OFF

//Only use `find_package` to get dependencies
CPM_LOCAL_PACKAGES_ONLY:BOOL=OFF

//Directory to download CPM dependencies
CPM_SOURCE_CACHE:PATH=OFF

//Always try to use `find_package` to get dependencies
CPM_USE_LOCAL_PACKAGES:BOOL=OFF

//Use additional directory of package name in cache on the most
// nested level.
CPM_USE_NAMED_CACHE_DIRECTORIES:BOOL=OFF

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for fmt
FETCHCONTENT_SOURCE_DIR_FMT:PATH=

//When not empty, overrides where to find pre-populated content
// for json
FETCHCONTENT_SOURCE_DIR_JSON:PATH=

//When not empty, overrides where to find pre-populated content
// for result
FETCHCONTENT_SOURCE_DIR_RESULT:PATH=

//When not empty, overrides where to find pre-populated content
// for TulipHook
FETCHCONTENT_SOURCE_DIR_TULIPHOOK:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of fmt
FETCHCONTENT_UPDATES_DISCONNECTED_FMT:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of json
FETCHCONTENT_UPDATES_DISCONNECTED_JSON:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of result
FETCHCONTENT_UPDATES_DISCONNECTED_RESULT:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of TulipHook
FETCHCONTENT_UPDATES_DISCONNECTED_TULIPHOOK:BOOL=OFF

//Value Computed by CMake
FMT_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/fmt-build

//Installation directory for cmake files, a relative path that
// will be joined with C:/Users/<USER>/source/repos/Paibotgeode/out/install/x64-debug
// or an absolute path.
FMT_CMAKE_DIR:STRING=lib/cmake/fmt

//Generate the cuda-test target.
FMT_CUDA_TEST:BOOL=OFF

//Debug library postfix.
FMT_DEBUG_POSTFIX:STRING=d

//Generate the doc target.
FMT_DOC:BOOL=OFF

//Generate the fuzz target.
FMT_FUZZ:BOOL=OFF

//Installation directory for include files, a relative path that
// will be joined with C:/Users/<USER>/source/repos/Paibotgeode/out/install/x64-debug
// or an absolute path.
FMT_INC_DIR:STRING=include

//Generate the install target.
FMT_INSTALL:BOOL=ON

//Value Computed by CMake
FMT_IS_TOP_LEVEL:STATIC=OFF

//Installation directory for libraries, a relative path that will
// be joined to C:/Users/<USER>/source/repos/Paibotgeode/out/install/x64-debug
// or an absolute path.
FMT_LIB_DIR:STRING=lib

//Build a module instead of a traditional library.
FMT_MODULE:BOOL=OFF

//Include OS-specific APIs.
FMT_OS:BOOL=ON

//Enable extra warnings and expensive tests.
FMT_PEDANTIC:BOOL=OFF

//Installation directory for pkgconfig (.pc) files, a relative
// path that will be joined with C:/Users/<USER>/source/repos/Paibotgeode/out/install/x64-debug
// or an absolute path.
FMT_PKGCONFIG_DIR:STRING=lib/pkgconfig

//Value Computed by CMake
FMT_SOURCE_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/fmt-src

//Expose headers with marking them as system.
FMT_SYSTEM_HEADERS:BOOL=OFF

//Generate the test target.
FMT_TEST:BOOL=OFF

//Enable Unicode support.
FMT_UNICODE:BOOL=ON

//Halt the compilation with an error on compiler warnings.
FMT_WERROR:BOOL=OFF

//Path to a program.
GEODE_CCACHE:FILEPATH=GEODE_CCACHE-NOTFOUND

//Path to a program.
GEODE_CLI:FILEPATH=C:/Users/<USER>/scoop/shims/geode.exe

GEODE_MOD_BINARY_SUFFIX:STRING=.dll

//Enables the use of the Breakpad library for crash dumps.
GEODE_USE_BREAKPAD:BOOL=ON

//Path to a program.
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Value Computed by CMake
GeodeBindings_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/bindings

//Value Computed by CMake
GeodeBindings_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
GeodeBindings_SOURCE_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/bindings-src

//Value Computed by CMake
GeodeResult_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/result-build

//Value Computed by CMake
GeodeResult_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
GeodeResult_SOURCE_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/result-src

//Build TulipHook as a dynamic library
TULIP_BUILD_DYNAMIC:BOOL=OFF

//Link to TulipHook source files, including external libs
TULIP_LINK_SOURCE:BOOL=OFF

//Value Computed by CMake
TulipHookRoot_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-build

//Value Computed by CMake
TulipHookRoot_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
TulipHookRoot_SOURCE_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-src

//Value Computed by CMake
geode-sdk_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/geode

//Value Computed by CMake
geode-sdk_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
geode-sdk_SOURCE_DIR:STATIC=C:/Users/<USER>/mod-dev

//Value Computed by CMake
mat-json_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/json-build

//Value Computed by CMake
mat-json_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
mat-json_SOURCE_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/json-src

//Value Computed by CMake
xdBot2_BINARY_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug

//Value Computed by CMake
xdBot2_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
xdBot2_SOURCE_DIR:STATIC=C:/Users/<USER>/source/repos/Paibotgeode


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=7
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
CPM_DIRECTORY:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/cmake
//Don't download or configure dependencies (for testing)
CPM_DRY_RUN:INTERNAL=OFF
CPM_FILE:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/cmake/CPM_0.40.2.cmake
CPM_INDENT:INTERNAL=CPM:
CPM_MODULE_PATH:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CPM_modules
CPM_PACKAGES:INTERNAL=bindings;result;json;fmt;TulipHook
CPM_PACKAGE_LOCK_FILE:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/cpm-package-lock.cmake
CPM_PACKAGE_TulipHook_BINARY_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-build
CPM_PACKAGE_TulipHook_SOURCE_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-src
CPM_PACKAGE_TulipHook_VERSION:INTERNAL=3.1.4
CPM_PACKAGE_bindings_BINARY_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/bindings-build
CPM_PACKAGE_bindings_SOURCE_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/bindings-src
CPM_PACKAGE_bindings_VERSION:INTERNAL=
CPM_PACKAGE_fmt_BINARY_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/fmt-build
CPM_PACKAGE_fmt_SOURCE_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/fmt-src
CPM_PACKAGE_fmt_VERSION:INTERNAL=11.1.4
CPM_PACKAGE_json_BINARY_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/json-build
CPM_PACKAGE_json_SOURCE_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/json-src
CPM_PACKAGE_json_VERSION:INTERNAL=3.2.1
CPM_PACKAGE_result_BINARY_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/result-build
CPM_PACKAGE_result_SOURCE_DIR:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/result-src
CPM_PACKAGE_result_VERSION:INTERNAL=1.3.3
CPM_VERSION:INTERNAL=0.40.2
//GEODE_CLI_VERSION
GEODE_CLI_VERSION:INTERNAL=3.7.0
//GEODE_MODS_BEING_BUILT
GEODE_MODS_BEING_BUILT:INTERNAL=paimon.scaler:2.0.0
//Geode version
GEODE_VERSION:INTERNAL=4.8.0
//Geode version full
GEODE_VERSION_FULL:INTERNAL=4.8.0
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Test SUPPORTS_W_NO_EVERYTHING
SUPPORTS_W_NO_EVERYTHING:INTERNAL=
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Users/<USER>/source/repos/Paibotgeode/out/install/x64-debug

