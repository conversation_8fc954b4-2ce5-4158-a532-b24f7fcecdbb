# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: tuliphook-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\fg906\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild\

#############################################
# Utility command for tuliphook-populate

build tuliphook-populate: phony CMakeFiles\tuliphook-populate CMakeFiles\tuliphook-populate-complete tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-done tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-configure tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-download tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-mkdir tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-test tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles\tuliphook-populate

build CMakeFiles\tuliphook-populate | ${cmake_ninja_workdir}CMakeFiles\tuliphook-populate: phony CMakeFiles\tuliphook-populate-complete


#############################################
# Custom command for CMakeFiles\tuliphook-populate-complete

build CMakeFiles\tuliphook-populate-complete tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-done | ${cmake_ninja_workdir}CMakeFiles\tuliphook-populate-complete ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-done: CUSTOM_COMMAND tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-mkdir tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-download tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-configure tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-test
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/CMakeFiles && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/CMakeFiles/tuliphook-populate-complete && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-done"
  DESC = Completed 'tuliphook-populate'
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-build

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-build | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-build: CUSTOM_COMMAND tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-configure
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-build"
  DESC = No build step for 'tuliphook-populate'
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-configure

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-configure | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-configure: CUSTOM_COMMAND tuliphook-populate-prefix\tmp\tuliphook-populate-cfgcmd.txt tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-configure"
  DESC = No configure step for 'tuliphook-populate'
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-download

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-download | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-download: CUSTOM_COMMAND tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-gitinfo.txt tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-mkdir
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/tmp/tuliphook-populate-gitclone.cmake && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-download"
  DESC = Performing download step (git clone) for 'tuliphook-populate'
  pool = console
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install: CUSTOM_COMMAND tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-build
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-install"
  DESC = No install step for 'tuliphook-populate'
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-mkdir

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-mkdir | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-mkdir: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -Dcfgdir= -P C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/tmp/tuliphook-populate-mkdirs.cmake && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-mkdir"
  DESC = Creating directories for 'tuliphook-populate'
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch: CUSTOM_COMMAND tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-patch-info.txt tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-subbuild && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-patch"
  DESC = No patch step for 'tuliphook-populate'
  pool = console
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-test

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-test | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-test: CUSTOM_COMMAND tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-install
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/src/tuliphook-populate-stamp/tuliphook-populate-test"
  DESC = No test step for 'tuliphook-populate'
  restat = 1


#############################################
# Custom command for tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update

build tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update | ${cmake_ninja_workdir}tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update: CUSTOM_COMMAND tuliphook-populate-prefix\tmp\tuliphook-populate-gitupdate.cmake tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-update-info.txt tuliphook-populate-prefix\src\tuliphook-populate-stamp\tuliphook-populate-download
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild/tuliphook-populate-prefix/tmp/tuliphook-populate-gitupdate.cmake"
  DESC = Performing update step for 'tuliphook-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild

build codegen: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-subbuild

build all: phony tuliphook-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\PatchInfo.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\RepositoryInfo.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\UpdateInfo.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\cfgcmd.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\gitclone.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\gitupdate.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\mkdirs.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake CMakeLists.txt tuliphook-populate-prefix\tmp\tuliphook-populate-mkdirs.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\PatchInfo.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\RepositoryInfo.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\UpdateInfo.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\cfgcmd.txt.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\gitclone.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\gitupdate.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\mkdirs.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake CMakeLists.txt tuliphook-populate-prefix\tmp\tuliphook-populate-mkdirs.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
