#pragma once

#include <Geode/Geode.hpp>
// #include <Geode/loader/SettingEvent.hpp>

#include <string>
#include <thread>
#include <mutex>
#include <queue>
#include <cmath>
#include <vector>
#include <filesystem>

#include "renderer/renderer.hpp"
#include "macro.hpp"

using namespace geode::prelude;

const int seedAddr = 0x6a4e20;

const int indexButton[6] = { 1, 2, 3, 1, 2, 3 };

inline bool isXdBotMacroName(const std::string& name) {
    return name == "xdBot" || name == "Paimon Scaler Mod";
}

const std::map<int, int> buttonIndex[2] = { { {1, 0}, {2, 1}, {3, 2} }, { {1, 3}, {2, 4}, {3, 5} } };

const int sidesButtons[4] = { 1, 2, 4, 5 };

const std::string buttonIDs[6] = {
    "robtop.geometry-dash/jump-p1",
    "robtop.geometry-dash/move-left-p1",
    "robtop.geometry-dash/move-right-p1",
    "robtop.geometry-dash/jump-p2",
    "robtop.geometry-dash/move-left-p2",
    "robtop.geometry-dash/move-right-p2"
};

#define STATIC_CREATE(class, width, height) \
    static class* create() { \
        class* ret = new class(); \
        if (ret->initAnchored(width, height, Utils::getTexture().c_str())) { \
            ret->autorelease(); \
            return ret; \
        } \
        delete ret; \
        return nullptr; \
    }

class Global {

    Global() {}

public:

    static auto& get() {
        static Global instance;
        return instance;
    }

    static bool hasIncompatibleMods();

    static float getTPS();

    static int getCurrentFrame(bool editor = false);

    static void updateKeybinds();

    static void updateSeed(bool isRestart = false);

    static void updatePitch(float value);

    static void toggleSpeedhack();

    static void frameStep();

    static void toggleFrameStepper();

    static void frameStepperOn();

    static void frameStepperOff();

    static void forceSafeModeOff();

    static void forceSpeedhackOff();

    static void forceNormalSpeed();

    static PauseLayer* getPauseLayer();

    Mod* mod = Mod::get();
    geode::Popup<>* layer = nullptr;

    Macro macro;
    Renderer renderer;
    state state = none;

    std::unordered_map<CheckpointObject*, CheckpointData> checkpoints;
    std::unordered_set<int> allKeybinds;
    std::unordered_set<int> playedFrames;
    std::vector<int> keybinds[6];

    int lastAutoSaveFrame = 0;
    std::chrono::time_point<std::chrono::steady_clock> lastAutoSaveMS = std::chrono::steady_clock::now();
    int currentSession = 0;

    bool stepFrame = false;
    bool stepFrameDraw = false;
    int stepFrameDrawMultiple = 0;
    int stepFrameParticle = 0;
    int frameStepperMusicTime = 0;

    bool cancelCheckpoint = false;
    bool ignoreRecordAction = false;
    bool restart = false;
    bool restartLater = false;
    bool creatingTrajectory = false;
    bool firstAttempt = false;

    bool disableShaders = false;
    bool safeMode = false;
    bool layoutMode = false;
    bool showTrajectory = false;
    bool coinFinder = false;
    bool frameStepper = false;
    bool speedhackEnabled = false;
    bool speedhackAudio = false;
    bool seedEnabled = false;
    bool clickbotEnabled = false;
    bool clickbotOnlyPlaying = false;
    bool clickbotOnlyHolding = false;
    bool frameLabel = false;
    bool trajectoryBothSides = false;
    bool p2mirror = false;
    bool lockDelta = false;
    bool stopPlaying = false;
    bool tpsEnabled = false;
    float tps = 240.f;
    bool previousTpsEnabled = false;
    float previousTps = 0.f;
    bool autoclicker = false;
    bool autoclickerP1 = false;
    bool autoclickerP2 = false;
    int holdFor = 0;
    int releaseFor = 0;
    int holdFor2 = 0;
    int releaseFor2 = 0;
    bool autosaveIntervalEnabled = false;
    int autosaveInterval = 600000;
    float autosaveCheck = 2.f;
    bool autosaveEnabled = false;

    std::filesystem::path autoLoadedMacroPath;
    std::vector<std::filesystem::path> autoLoadedMacroPool;
    std::string autoLoadedLevelKey;
    bool macroLoadedAutomatically = false;
    bool macroJustRecorded = false;

    bool ignoreStopDashing[2] = { false, false };
    bool addSideHoldingMembers[2] = { false, false };
    bool wasHolding[6] = { false, false, false, false, false, false };
    bool heldButtons[6] = { false, false, false, false, false, false };

    int delayedFrameRelease[2][2] = { { -1, -1 }, { -1, -1 } };
    int delayedFrameReleaseMain[2] = { -1, -1 };
    int delayedFrameInput[2] = { -1, -1 };
    int ignoreFrame = -1;
    int respawnFrame = -1;
    int ignoreJumpButton = -1;
    int frameOffset = 0;
    int previousFrame = 0;

    size_t currentAction = 0;
    size_t currentFrameFix = 0;
    int frameFixesLimit = 240;
    bool frameFixes = false;
    bool inputFixes = false;

    int currentPage = 0;
    float currentPitch = 1.f;
    uintptr_t latestSeed = 0;
    float leftOver = 0.f;

    // Sistema híbrido macro+trayectoria para clicks más humanos
    bool smartMacroEnabled = true;
    int humanVariationFrames = 3; // ±3 frames de variación humana
    float trajectoryPredictionDistance = 50.0f; // Distancia para predecir clicks
    bool useTrajectoryGuide = true;
    std::vector<CCPoint> predictedClickPoints;
    int lastPredictedFrame = -1;

    // Sistema de indetectabilidad
    bool stealthMode = false;
    bool hideMenuButton = false;
    bool disableNotifications = false;
    bool randomizeKeybinds = false;
    int stealthLevel = 0; // 0 = normal, 1 = oculto, 2 = invisible

    // Funciones cosméticas
    bool cosmeticAllFriends = true;

    // Sistema de respaldo ultra robusto
    struct PendingClick {
        int button;
        bool player2;
        bool down;
        int originalFrame;
        int attemptsLeft;
    };
    std::vector<PendingClick> pendingClicks;
    int maxClickAttempts = 10; // Máximo 10 intentos por click

    // SISTEMA DE HUMANIZACIÓN AVANZADA
    struct HumanBehavior {
        float fatigueLevel = 0.0f; // 0.0 = fresco, 1.0 = muy cansado
        int consecutiveAttempts = 0; // Intentos consecutivos en el nivel
        int totalPlayTime = 0; // Tiempo total jugando (frames)
        float stressLevel = 0.0f; // 0.0 = calmado, 1.0 = muy estresado
        int recentDeaths = 0; // Muertes recientes
        float learningProgress = 0.0f; // 0.0 = principiante, 1.0 = experto en este nivel

        // Patrones de error humano
        int lastErrorFrame = -100;
        bool inPanicMode = false;
        int panicFramesLeft = 0;
        float reactionTimeMs = 150.0f; // Tiempo de reacción base en ms

        // Micro-hábitos humanos
        bool tendsToSpam = false; // Tendencia a hacer spam de clicks
        bool tendsToHold = false; // Tendencia a mantener presionado
        float anticipationBias = 0.0f; // Tendencia a clickear antes o después
    } humanBehavior;
};