# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: xdBot2
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\fg906\source\repos\Paibotgeode\out\build\x64-debug\
# =============================================================================
# Object build statements for SHARED_LIBRARY target xdBot2


#############################################
# Order-only phony target for xdBot2

build cmake_object_order_depends_target_xdBot2: phony || cmake_object_order_depends_target_GeodeBindings cmake_object_order_depends_target_fmt

build CMakeFiles\xdBot2.dir\cmake_pch.cxx.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\out\build\x64-debug\CMakeFiles\xdBot2.dir\cmake_pch.cxx | CMakeFiles\xdBot2.dir\cmake_pch.hxx || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YcC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb


#############################################
# Additional output files.

build CMakeFiles\xdBot2.dir\.\cmake_pch.cxx.pch: phony CMakeFiles\xdBot2.dir\cmake_pch.cxx.obj

build CMakeFiles\xdBot2.dir\src\main.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\main.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\global.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\global.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\macro.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\macro.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\keybinds.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\keybinds.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\utils\utils.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\utils\utils.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\gdr\gdr.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\gdr\gdr.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\gdr
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\renderer\renderer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\renderer\renderer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\renderer
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\other.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\other.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\tps_bypass.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\tps_bypass.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\frame_stepper.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\frame_stepper.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\layout_mode.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\layout_mode.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\show_trajectory.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\show_trajectory.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\coin_finder.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\coin_finder.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\clickbot.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\clickbot.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\autoclicker.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\autoclicker.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\hacks\friend_cosmetic.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\hacks\friend_cosmetic.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\hacks
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\macro_editor.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\macro_editor.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\button_edit_layer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\button_edit_layer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\game_ui.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\game_ui.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\record_layer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\record_layer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\render_settings_layer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\render_settings_layer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\load_macro_layer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\load_macro_layer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\clickbot_layer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\clickbot_layer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\ui\button_setting.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\ui\button_setting.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\ui
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\practice_fixes\input.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\practice_fixes\input.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\practice_fixes
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\practice_fixes\player.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\practice_fixes\player.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\practice_fixes
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\src\practice_fixes\play_layer.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\src\practice_fixes\play_layer.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\src\practice_fixes
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb

build CMakeFiles\xdBot2.dir\C_\Users\fg906\mod-dev\entry.cpp.obj: CXX_COMPILER__xdBot2_unscanned_Debug C$:\Users\fg906\mod-dev\entry.cpp | CMakeFiles\xdBot2.dir\cmake_pch.hxx CMakeFiles\xdBot2.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_xdBot2
  DEFINES = -DGEODE_COMP_GD_VERSION=22074 -DGEODE_GD_VERSION=2.2074 -DGEODE_GD_VERSION_STRING=\"2.2074\" -DGEODE_MOD_ID=\"paimon.scaler\" -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -DMAT_JSON_DYNAMIC=1 -DNOMINMAX -DPROJECT_NAME=xdBot2 -D_HAS_ITERATOR_DEBUGGING=0 -DxdBot2_EXPORTS
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8 /YuC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx /FpC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/./cmake_pch.cxx.pch /FIC:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/CMakeFiles/xdBot2.dir/cmake_pch.hxx
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode-deps -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  OBJECT_FILE_DIR = CMakeFiles\xdBot2.dir\C_\Users\fg906\mod-dev
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_PDB = paimon.scaler.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target xdBot2


#############################################
# Link the shared library paimon.scaler.dll

build paimon.scaler.dll paimon.scaler.lib: CXX_SHARED_LIBRARY_LINKER__xdBot2_Debug CMakeFiles\xdBot2.dir\cmake_pch.cxx.obj CMakeFiles\xdBot2.dir\src\main.cpp.obj CMakeFiles\xdBot2.dir\src\global.cpp.obj CMakeFiles\xdBot2.dir\src\macro.cpp.obj CMakeFiles\xdBot2.dir\src\keybinds.cpp.obj CMakeFiles\xdBot2.dir\src\utils\utils.cpp.obj CMakeFiles\xdBot2.dir\src\gdr\gdr.cpp.obj CMakeFiles\xdBot2.dir\src\renderer\renderer.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\other.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\tps_bypass.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\frame_stepper.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\layout_mode.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\show_trajectory.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\coin_finder.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\clickbot.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\autoclicker.cpp.obj CMakeFiles\xdBot2.dir\src\hacks\friend_cosmetic.cpp.obj CMakeFiles\xdBot2.dir\src\ui\macro_editor.cpp.obj CMakeFiles\xdBot2.dir\src\ui\button_edit_layer.cpp.obj CMakeFiles\xdBot2.dir\src\ui\game_ui.cpp.obj CMakeFiles\xdBot2.dir\src\ui\record_layer.cpp.obj CMakeFiles\xdBot2.dir\src\ui\render_settings_layer.cpp.obj CMakeFiles\xdBot2.dir\src\ui\load_macro_layer.cpp.obj CMakeFiles\xdBot2.dir\src\ui\clickbot_layer.cpp.obj CMakeFiles\xdBot2.dir\src\ui\button_setting.cpp.obj CMakeFiles\xdBot2.dir\src\practice_fixes\input.cpp.obj CMakeFiles\xdBot2.dir\src\practice_fixes\player.cpp.obj CMakeFiles\xdBot2.dir\src\practice_fixes\play_layer.cpp.obj CMakeFiles\xdBot2.dir\C_\Users\fg906\mod-dev\entry.cpp.obj | geode-deps\geode.custom-keybinds\geode.custom-keybinds.lib C$:\Users\fg906\mod-dev\loader\include\link\win64\libcocos2d.lib C$:\Users\fg906\mod-dev\loader\include\link\win64\libExtensions.lib C$:\Users\fg906\mod-dev\loader\include\link\win64\glew32.lib C$:\Users\fg906\mod-dev\loader\include\link\win64\fmod.lib C$:\Users\fg906\mod-dev\loader\include\link\win64\nghttp2.lib C$:\Users\fg906\mod-dev\loader\include\link\win64\libcurl.lib bindings\GeodeBindings.lib _deps\fmt-build\fmtd.lib C$:\Users\fg906\mod-dev\bin\4.8.0\Geode.lib || _deps\fmt-build\fmtd.lib bindings\GeodeBindings.lib
  LANGUAGE_COMPILE_FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL
  LINK_LIBRARIES = geode-deps\geode.custom-keybinds\geode.custom-keybinds.lib  C:\Users\<USER>\mod-dev\loader\include\link\win64\libcocos2d.lib  C:\Users\<USER>\mod-dev\loader\include\link\win64\libExtensions.lib  C:\Users\<USER>\mod-dev\loader\include\link\win64\glew32.lib  C:\Users\<USER>\mod-dev\loader\include\link\win64\fmod.lib  opengl32.lib  C:\Users\<USER>\mod-dev\loader\include\link\win64\nghttp2.lib  C:\Users\<USER>\mod-dev\loader\include\link\win64\libcurl.lib  delayimp.lib  ws2_32.lib  bindings\GeodeBindings.lib  _deps\fmt-build\fmtd.lib  C:\Users\<USER>\mod-dev\bin\4.8.0\Geode.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\mod-dev\loader\include\link
  OBJECT_DIR = CMakeFiles\xdBot2.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  RESTAT = 1
  TARGET_COMPILE_PDB = CMakeFiles\xdBot2.dir\
  TARGET_FILE = paimon.scaler.dll
  TARGET_IMPLIB = paimon.scaler.lib
  TARGET_PDB = paimon.scaler.pdb


#############################################
# Utility command for xdBot2_PACKAGE

build xdBot2_PACKAGE: phony CMakeFiles\xdBot2_PACKAGE paimon.scaler.dll


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles\install.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles\install.util


#############################################
# Utility command for install/local

build CMakeFiles\install\local.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install\local: phony CMakeFiles\install\local.util


#############################################
# Custom command for CMakeFiles\xdBot2_PACKAGE

build CMakeFiles\xdBot2_PACKAGE | ${cmake_ninja_workdir}CMakeFiles\xdBot2_PACKAGE: CUSTOM_COMMAND paimon.scaler.dll C$:\Users\fg906\source\repos\Paibotgeode\mod.json || _deps\fmt-build\fmtd.lib bindings\GeodeBindings.lib paimon.scaler.dll
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug && C:\Users\<USER>\scoop\shims\geode.exe package new C:/Users/<USER>/source/repos/Paibotgeode --binary C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/paimon.scaler.dll --output C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/paimon.scaler.geode --install"
  pool = console

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/source/repos/Paibotgeode/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build geode\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build geode\edit_cache: phony geode\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build geode\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build geode\rebuild_cache: phony geode\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build geode\list_install_components: phony


#############################################
# Utility command for install

build geode\CMakeFiles\install.util: CUSTOM_COMMAND geode\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build geode\install: phony geode\CMakeFiles\install.util


#############################################
# Utility command for install/local

build geode\CMakeFiles\install\local.util: CUSTOM_COMMAND geode\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\geode && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build geode\install\local: phony geode\CMakeFiles\install\local.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/mod-dev/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _deps\result-build\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build _deps\result-build\edit_cache: phony _deps\result-build\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps\result-build\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps\result-build\rebuild_cache: phony _deps\result-build\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps\result-build\list_install_components: phony


#############################################
# Utility command for install

build _deps\result-build\CMakeFiles\install.util: CUSTOM_COMMAND _deps\result-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build _deps\result-build\install: phony _deps\result-build\CMakeFiles\install.util


#############################################
# Utility command for install/local

build _deps\result-build\CMakeFiles\install\local.util: CUSTOM_COMMAND _deps\result-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps\result-build\install\local: phony _deps\result-build\CMakeFiles\install\local.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/mod-dev/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _deps\json-build\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build _deps\json-build\edit_cache: phony _deps\json-build\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps\json-build\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps\json-build\rebuild_cache: phony _deps\json-build\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps\json-build\list_install_components: phony


#############################################
# Utility command for install

build _deps\json-build\CMakeFiles\install.util: CUSTOM_COMMAND _deps\json-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build _deps\json-build\install: phony _deps\json-build\CMakeFiles\install.util


#############################################
# Utility command for install/local

build _deps\json-build\CMakeFiles\install\local.util: CUSTOM_COMMAND _deps\json-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps\json-build\install\local: phony _deps\json-build\CMakeFiles\install\local.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/mod-dev/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target fmt


#############################################
# Order-only phony target for fmt

build cmake_object_order_depends_target_fmt: phony || .

build _deps\fmt-build\CMakeFiles\fmt.dir\src\format.cc.obj: CXX_COMPILER__fmt_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\src\format.cc || cmake_object_order_depends_target_fmt
  DEFINES = -D_HAS_ITERATOR_DEBUGGING=0
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /utf-8
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include
  OBJECT_DIR = _deps\fmt-build\CMakeFiles\fmt.dir
  OBJECT_FILE_DIR = _deps\fmt-build\CMakeFiles\fmt.dir\src
  TARGET_COMPILE_PDB = fmtd.pdb
  TARGET_PDB = _deps\fmt-build\fmtd.pdb

build _deps\fmt-build\CMakeFiles\fmt.dir\src\os.cc.obj: CXX_COMPILER__fmt_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\src\os.cc || cmake_object_order_depends_target_fmt
  DEFINES = -D_HAS_ITERATOR_DEBUGGING=0
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /utf-8
  INCLUDES = -IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include
  OBJECT_DIR = _deps\fmt-build\CMakeFiles\fmt.dir
  OBJECT_FILE_DIR = _deps\fmt-build\CMakeFiles\fmt.dir\src
  TARGET_COMPILE_PDB = fmtd.pdb
  TARGET_PDB = _deps\fmt-build\fmtd.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target fmt


#############################################
# Link the static library _deps\fmt-build\fmtd.lib

build _deps\fmt-build\fmtd.lib: CXX_STATIC_LIBRARY_LINKER__fmt_Debug _deps\fmt-build\CMakeFiles\fmt.dir\src\format.cc.obj _deps\fmt-build\CMakeFiles\fmt.dir\src\os.cc.obj
  LANGUAGE_COMPILE_FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64
  OBJECT_DIR = _deps\fmt-build\CMakeFiles\fmt.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = fmtd.pdb
  TARGET_FILE = _deps\fmt-build\fmtd.lib
  TARGET_PDB = _deps\fmt-build\fmtd.pdb


#############################################
# Utility command for edit_cache

build _deps\fmt-build\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build _deps\fmt-build\edit_cache: phony _deps\fmt-build\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps\fmt-build\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps\fmt-build\rebuild_cache: phony _deps\fmt-build\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps\fmt-build\list_install_components: phony


#############################################
# Utility command for install

build _deps\fmt-build\CMakeFiles\install.util: CUSTOM_COMMAND _deps\fmt-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build _deps\fmt-build\install: phony _deps\fmt-build\CMakeFiles\install.util


#############################################
# Utility command for install/local

build _deps\fmt-build\CMakeFiles\install\local.util: CUSTOM_COMMAND _deps\fmt-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps\fmt-build\install\local: phony _deps\fmt-build\CMakeFiles\install\local.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/mod-dev/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _deps\tuliphook-build\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build _deps\tuliphook-build\edit_cache: phony _deps\tuliphook-build\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps\tuliphook-build\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps\tuliphook-build\rebuild_cache: phony _deps\tuliphook-build\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps\tuliphook-build\list_install_components: phony


#############################################
# Utility command for install

build _deps\tuliphook-build\CMakeFiles\install.util: CUSTOM_COMMAND _deps\tuliphook-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build _deps\tuliphook-build\install: phony _deps\tuliphook-build\CMakeFiles\install.util


#############################################
# Utility command for install/local

build _deps\tuliphook-build\CMakeFiles\install\local.util: CUSTOM_COMMAND _deps\tuliphook-build\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-build && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps\tuliphook-build\install\local: phony _deps\tuliphook-build\CMakeFiles\install\local.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/mod-dev/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GeodeBindings


#############################################
# Order-only phony target for GeodeBindings

build cmake_object_order_depends_target_GeodeBindings: phony || bindings\bindings\Geode\CodegenData.txt bindings\bindings\Geode\GeneratedBinding.hpp bindings\bindings\Geode\GeneratedModify.hpp bindings\bindings\Geode\GeneratedPredeclare.hpp bindings\bindings\Geode\GeneratedSource.cpp bindings\bindings\Geode\binding\AccountHelpLayer.hpp bindings\bindings\Geode\binding\AccountLayer.hpp bindings\bindings\Geode\binding\AccountLoginLayer.hpp bindings\bindings\Geode\binding\AccountRegisterLayer.hpp bindings\bindings\Geode\binding\AchievementBar.hpp bindings\bindings\Geode\binding\AchievementCell.hpp bindings\bindings\Geode\binding\AchievementManager.hpp bindings\bindings\Geode\binding\AchievementNotifier.hpp bindings\bindings\Geode\binding\AchievementsLayer.hpp bindings\bindings\Geode\binding\AdToolbox.hpp bindings\bindings\Geode\binding\AdvFollowSetup.hpp bindings\bindings\Geode\binding\AdvancedFollowEditObject.hpp bindings\bindings\Geode\binding\AdvancedFollowInstance.hpp bindings\bindings\Geode\binding\AdvancedFollowTriggerObject.hpp bindings\bindings\Geode\binding\AnimatedGameObject.hpp bindings\bindings\Geode\binding\AnimatedShopKeeper.hpp bindings\bindings\Geode\binding\AnimatedSpriteDelegate.hpp bindings\bindings\Geode\binding\AppDelegate.hpp bindings\bindings\Geode\binding\ArtTriggerGameObject.hpp bindings\bindings\Geode\binding\ArtistCell.hpp bindings\bindings\Geode\binding\AudioAssetsBrowser.hpp bindings\bindings\Geode\binding\AudioEffectsLayer.hpp bindings\bindings\Geode\binding\AudioLineGuideGameObject.hpp bindings\bindings\Geode\binding\BitmapFontCache.hpp bindings\bindings\Geode\binding\BonusDropdown.hpp bindings\bindings\Geode\binding\BoomListLayer.hpp bindings\bindings\Geode\binding\BoomListView.hpp bindings\bindings\Geode\binding\BoomScrollLayer.hpp bindings\bindings\Geode\binding\BoomScrollLayerDelegate.hpp bindings\bindings\Geode\binding\BrowseSmartKeyLayer.hpp bindings\bindings\Geode\binding\BrowseSmartTemplateLayer.hpp bindings\bindings\Geode\binding\ButtonPage.hpp bindings\bindings\Geode\binding\ButtonSprite.hpp bindings\bindings\Geode\binding\CAState.hpp bindings\bindings\Geode\binding\CCAlertCircle.hpp bindings\bindings\Geode\binding\CCAnimateFrameCache.hpp bindings\bindings\Geode\binding\CCAnimatedSprite.hpp bindings\bindings\Geode\binding\CCBlockLayer.hpp bindings\bindings\Geode\binding\CCCircleAlert.hpp bindings\bindings\Geode\binding\CCCircleWave.hpp bindings\bindings\Geode\binding\CCCircleWaveDelegate.hpp bindings\bindings\Geode\binding\CCContentLayer.hpp bindings\bindings\Geode\binding\CCCountdown.hpp bindings\bindings\Geode\binding\CCCounterLabel.hpp bindings\bindings\Geode\binding\CCExtenderNode.hpp bindings\bindings\Geode\binding\CCIndexPath.hpp bindings\bindings\Geode\binding\CCLightFlash.hpp bindings\bindings\Geode\binding\CCLightStrip.hpp bindings\bindings\Geode\binding\CCMenuItemSpriteExtra.hpp bindings\bindings\Geode\binding\CCMenuItemToggler.hpp bindings\bindings\Geode\binding\CCMoveCNode.hpp bindings\bindings\Geode\binding\CCNodeContainer.hpp bindings\bindings\Geode\binding\CCPartAnimSprite.hpp bindings\bindings\Geode\binding\CCScrollLayerExt.hpp bindings\bindings\Geode\binding\CCScrollLayerExtDelegate.hpp bindings\bindings\Geode\binding\CCSpriteCOpacity.hpp bindings\bindings\Geode\binding\CCSpriteGrayscale.hpp bindings\bindings\Geode\binding\CCSpritePart.hpp bindings\bindings\Geode\binding\CCSpritePlus.hpp bindings\bindings\Geode\binding\CCSpriteWithHue.hpp bindings\bindings\Geode\binding\CCTextInputNode.hpp bindings\bindings\Geode\binding\CCURLObject.hpp bindings\bindings\Geode\binding\CameraTriggerGameObject.hpp bindings\bindings\Geode\binding\ChallengeNode.hpp bindings\bindings\Geode\binding\ChallengesPage.hpp bindings\bindings\Geode\binding\ChanceObject.hpp bindings\bindings\Geode\binding\ChanceTriggerGameObject.hpp bindings\bindings\Geode\binding\CharacterColorDelegate.hpp bindings\bindings\Geode\binding\CharacterColorPage.hpp bindings\bindings\Geode\binding\CheckpointGameObject.hpp bindings\bindings\Geode\binding\CheckpointObject.hpp bindings\bindings\Geode\binding\CollisionBlockPopup.hpp bindings\bindings\Geode\binding\CollisionTriggerAction.hpp bindings\bindings\Geode\binding\ColorAction.hpp bindings\bindings\Geode\binding\ColorAction2.hpp bindings\bindings\Geode\binding\ColorActionSprite.hpp bindings\bindings\Geode\binding\ColorChannelSprite.hpp bindings\bindings\Geode\binding\ColorSelectDelegate.hpp bindings\bindings\Geode\binding\ColorSelectLiveOverlay.hpp bindings\bindings\Geode\binding\ColorSelectPopup.hpp bindings\bindings\Geode\binding\ColorSetupDelegate.hpp bindings\bindings\Geode\binding\CommentCell.hpp bindings\bindings\Geode\binding\CommentUploadDelegate.hpp bindings\bindings\Geode\binding\CommunityCreditNode.hpp bindings\bindings\Geode\binding\CommunityCreditsPage.hpp bindings\bindings\Geode\binding\ConfigureHSVWidget.hpp bindings\bindings\Geode\binding\ConfigureValuePopup.hpp bindings\bindings\Geode\binding\ConfigureValuePopupDelegate.hpp bindings\bindings\Geode\binding\CountTriggerAction.hpp bindings\bindings\Geode\binding\CountTriggerGameObject.hpp bindings\bindings\Geode\binding\CreateGuidelinesLayer.hpp bindings\bindings\Geode\binding\CreateMenuItem.hpp bindings\bindings\Geode\binding\CreateParticlePopup.hpp bindings\bindings\Geode\binding\CreatorLayer.hpp bindings\bindings\Geode\binding\CurrencyRewardDelegate.hpp bindings\bindings\Geode\binding\CurrencyRewardLayer.hpp bindings\bindings\Geode\binding\CurrencySprite.hpp bindings\bindings\Geode\binding\CustomListView.hpp bindings\bindings\Geode\binding\CustomMusicCell.hpp bindings\bindings\Geode\binding\CustomSFXCell.hpp bindings\bindings\Geode\binding\CustomSFXDelegate.hpp bindings\bindings\Geode\binding\CustomSFXWidget.hpp bindings\bindings\Geode\binding\CustomSongCell.hpp bindings\bindings\Geode\binding\CustomSongDelegate.hpp bindings\bindings\Geode\binding\CustomSongLayer.hpp bindings\bindings\Geode\binding\CustomSongLayerDelegate.hpp bindings\bindings\Geode\binding\CustomSongWidget.hpp bindings\bindings\Geode\binding\CustomizeObjectLayer.hpp bindings\bindings\Geode\binding\CustomizeObjectSettingsPopup.hpp bindings\bindings\Geode\binding\DailyLevelNode.hpp bindings\bindings\Geode\binding\DailyLevelPage.hpp bindings\bindings\Geode\binding\DashRingObject.hpp bindings\bindings\Geode\binding\DelayedSpawnNode.hpp bindings\bindings\Geode\binding\DemonFilterDelegate.hpp bindings\bindings\Geode\binding\DemonFilterSelectLayer.hpp bindings\bindings\Geode\binding\DemonInfoPopup.hpp bindings\bindings\Geode\binding\DialogDelegate.hpp bindings\bindings\Geode\binding\DialogLayer.hpp bindings\bindings\Geode\binding\DialogObject.hpp bindings\bindings\Geode\binding\DownloadMessageDelegate.hpp bindings\bindings\Geode\binding\DrawGridLayer.hpp bindings\bindings\Geode\binding\DungeonBarsSprite.hpp bindings\bindings\Geode\binding\DynamicBitset.hpp bindings\bindings\Geode\binding\DynamicMoveCalculation.hpp bindings\bindings\Geode\binding\DynamicObjectAction.hpp bindings\bindings\Geode\binding\DynamicScrollDelegate.hpp bindings\bindings\Geode\binding\EditButtonBar.hpp bindings\bindings\Geode\binding\EditGameObjectPopup.hpp bindings\bindings\Geode\binding\EditLevelLayer.hpp bindings\bindings\Geode\binding\EditTriggersPopup.hpp bindings\bindings\Geode\binding\EditorOptionsLayer.hpp bindings\bindings\Geode\binding\EditorPauseLayer.hpp bindings\bindings\Geode\binding\EditorUI.hpp bindings\bindings\Geode\binding\EffectGameObject.hpp bindings\bindings\Geode\binding\EffectManagerState.hpp bindings\bindings\Geode\binding\EndLevelLayer.hpp bindings\bindings\Geode\binding\EndPortalObject.hpp bindings\bindings\Geode\binding\EndTriggerGameObject.hpp bindings\bindings\Geode\binding\EnhancedGameObject.hpp bindings\bindings\Geode\binding\EnhancedTriggerObject.hpp bindings\bindings\Geode\binding\EnterEffectAnimValue.hpp bindings\bindings\Geode\binding\EnterEffectInstance.hpp bindings\bindings\Geode\binding\EnterEffectObject.hpp bindings\bindings\Geode\binding\EventLinkTrigger.hpp bindings\bindings\Geode\binding\EventTriggerInstance.hpp bindings\bindings\Geode\binding\ExplodeItemNode.hpp bindings\bindings\Geode\binding\ExplodeItemSprite.hpp bindings\bindings\Geode\binding\ExtendedLayer.hpp bindings\bindings\Geode\binding\FLAlertLayer.hpp bindings\bindings\Geode\binding\FLAlertLayerProtocol.hpp bindings\bindings\Geode\binding\FMODAudioEngine.hpp bindings\bindings\Geode\binding\FMODAudioState.hpp bindings\bindings\Geode\binding\FMODLevelVisualizer.hpp bindings\bindings\Geode\binding\FMODMusic.hpp bindings\bindings\Geode\binding\FMODQueuedEffect.hpp bindings\bindings\Geode\binding\FMODQueuedMusic.hpp bindings\bindings\Geode\binding\FMODSound.hpp bindings\bindings\Geode\binding\FMODSoundState.hpp bindings\bindings\Geode\binding\FMODSoundTween.hpp bindings\bindings\Geode\binding\FRequestProfilePage.hpp bindings\bindings\Geode\binding\FileOperation.hpp bindings\bindings\Geode\binding\FileSaveManager.hpp bindings\bindings\Geode\binding\FindBPMLayer.hpp bindings\bindings\Geode\binding\FindObjectPopup.hpp bindings\bindings\Geode\binding\FollowRewardPage.hpp bindings\bindings\Geode\binding\FontObject.hpp bindings\bindings\Geode\binding\ForceBlockGameObject.hpp bindings\bindings\Geode\binding\FriendRequestDelegate.hpp bindings\bindings\Geode\binding\FriendRequestPopup.hpp bindings\bindings\Geode\binding\FriendsProfilePage.hpp bindings\bindings\Geode\binding\GJAccountBackupDelegate.hpp bindings\bindings\Geode\binding\GJAccountDelegate.hpp bindings\bindings\Geode\binding\GJAccountLoginDelegate.hpp bindings\bindings\Geode\binding\GJAccountManager.hpp bindings\bindings\Geode\binding\GJAccountRegisterDelegate.hpp bindings\bindings\Geode\binding\GJAccountSettingsDelegate.hpp bindings\bindings\Geode\binding\GJAccountSettingsLayer.hpp bindings\bindings\Geode\binding\GJAccountSyncDelegate.hpp bindings\bindings\Geode\binding\GJActionManager.hpp bindings\bindings\Geode\binding\GJAssetDownloadAction.hpp bindings\bindings\Geode\binding\GJBaseGameLayer.hpp bindings\bindings\Geode\binding\GJBigSprite.hpp bindings\bindings\Geode\binding\GJBigSpriteNode.hpp bindings\bindings\Geode\binding\GJChallengeDelegate.hpp bindings\bindings\Geode\binding\GJChallengeItem.hpp bindings\bindings\Geode\binding\GJChestSprite.hpp bindings\bindings\Geode\binding\GJColorSetupLayer.hpp bindings\bindings\Geode\binding\GJComment.hpp bindings\bindings\Geode\binding\GJCommentListLayer.hpp bindings\bindings\Geode\binding\GJDailyLevelDelegate.hpp bindings\bindings\Geode\binding\GJDifficultySprite.hpp bindings\bindings\Geode\binding\GJDropDownLayer.hpp bindings\bindings\Geode\binding\GJDropDownLayerDelegate.hpp bindings\bindings\Geode\binding\GJEffectManager.hpp bindings\bindings\Geode\binding\GJFlyGroundLayer.hpp bindings\bindings\Geode\binding\GJFollowCommandLayer.hpp bindings\bindings\Geode\binding\GJFriendRequest.hpp bindings\bindings\Geode\binding\GJGameLevel.hpp bindings\bindings\Geode\binding\GJGameLoadingLayer.hpp bindings\bindings\Geode\binding\GJGameState.hpp bindings\bindings\Geode\binding\GJGarageLayer.hpp bindings\bindings\Geode\binding\GJGradientLayer.hpp bindings\bindings\Geode\binding\GJGroundLayer.hpp bindings\bindings\Geode\binding\GJHttpResult.hpp bindings\bindings\Geode\binding\GJItemIcon.hpp bindings\bindings\Geode\binding\GJLevelList.hpp bindings\bindings\Geode\binding\GJLevelScoreCell.hpp bindings\bindings\Geode\binding\GJListLayer.hpp bindings\bindings\Geode\binding\GJLocalLevelScoreCell.hpp bindings\bindings\Geode\binding\GJLocalScore.hpp bindings\bindings\Geode\binding\GJMGLayer.hpp bindings\bindings\Geode\binding\GJMPDelegate.hpp bindings\bindings\Geode\binding\GJMapObject.hpp bindings\bindings\Geode\binding\GJMapPack.hpp bindings\bindings\Geode\binding\GJMessageCell.hpp bindings\bindings\Geode\binding\GJMessagePopup.hpp bindings\bindings\Geode\binding\GJMoreGamesLayer.hpp bindings\bindings\Geode\binding\GJMultiplayerManager.hpp bindings\bindings\Geode\binding\GJObjectDecoder.hpp bindings\bindings\Geode\binding\GJOnlineRewardDelegate.hpp bindings\bindings\Geode\binding\GJOptionsLayer.hpp bindings\bindings\Geode\binding\GJPFollowCommandLayer.hpp bindings\bindings\Geode\binding\GJPathPage.hpp bindings\bindings\Geode\binding\GJPathRewardPopup.hpp bindings\bindings\Geode\binding\GJPathSprite.hpp bindings\bindings\Geode\binding\GJPathsLayer.hpp bindings\bindings\Geode\binding\GJPointDouble.hpp bindings\bindings\Geode\binding\GJPromoPopup.hpp bindings\bindings\Geode\binding\GJPurchaseDelegate.hpp bindings\bindings\Geode\binding\GJRequestCell.hpp bindings\bindings\Geode\binding\GJRewardDelegate.hpp bindings\bindings\Geode\binding\GJRewardItem.hpp bindings\bindings\Geode\binding\GJRewardObject.hpp bindings\bindings\Geode\binding\GJRobotSprite.hpp bindings\bindings\Geode\binding\GJRotateCommandLayer.hpp bindings\bindings\Geode\binding\GJRotationControl.hpp bindings\bindings\Geode\binding\GJRotationControlDelegate.hpp bindings\bindings\Geode\binding\GJScaleControl.hpp bindings\bindings\Geode\binding\GJScaleControlDelegate.hpp bindings\bindings\Geode\binding\GJScoreCell.hpp bindings\bindings\Geode\binding\GJSearchObject.hpp bindings\bindings\Geode\binding\GJShaderState.hpp bindings\bindings\Geode\binding\GJShopLayer.hpp bindings\bindings\Geode\binding\GJSmartBlockPreview.hpp bindings\bindings\Geode\binding\GJSmartBlockPreviewSprite.hpp bindings\bindings\Geode\binding\GJSmartPrefab.hpp bindings\bindings\Geode\binding\GJSmartTemplate.hpp bindings\bindings\Geode\binding\GJSongBrowser.hpp bindings\bindings\Geode\binding\GJSpecialColorSelect.hpp bindings\bindings\Geode\binding\GJSpecialColorSelectDelegate.hpp bindings\bindings\Geode\binding\GJSpiderSprite.hpp bindings\bindings\Geode\binding\GJSpriteColor.hpp bindings\bindings\Geode\binding\GJStoreItem.hpp bindings\bindings\Geode\binding\GJTransformControl.hpp bindings\bindings\Geode\binding\GJTransformControlDelegate.hpp bindings\bindings\Geode\binding\GJTransformState.hpp bindings\bindings\Geode\binding\GJUINode.hpp bindings\bindings\Geode\binding\GJUnlockableItem.hpp bindings\bindings\Geode\binding\GJUserCell.hpp bindings\bindings\Geode\binding\GJUserMessage.hpp bindings\bindings\Geode\binding\GJUserScore.hpp bindings\bindings\Geode\binding\GJValueTween.hpp bindings\bindings\Geode\binding\GJWorldNode.hpp bindings\bindings\Geode\binding\GJWriteMessagePopup.hpp bindings\bindings\Geode\binding\GManager.hpp bindings\bindings\Geode\binding\GameCell.hpp bindings\bindings\Geode\binding\GameEffectsManager.hpp bindings\bindings\Geode\binding\GameLevelManager.hpp bindings\bindings\Geode\binding\GameLevelOptionsLayer.hpp bindings\bindings\Geode\binding\GameManager.hpp bindings\bindings\Geode\binding\GameObject.hpp bindings\bindings\Geode\binding\GameObjectCopy.hpp bindings\bindings\Geode\binding\GameObjectEditorState.hpp bindings\bindings\Geode\binding\GameObjectPhysics.hpp bindings\bindings\Geode\binding\GameOptionsLayer.hpp bindings\bindings\Geode\binding\GameOptionsTrigger.hpp bindings\bindings\Geode\binding\GameRateDelegate.hpp bindings\bindings\Geode\binding\GameStatsManager.hpp bindings\bindings\Geode\binding\GameToolbox.hpp bindings\bindings\Geode\binding\GauntletLayer.hpp bindings\bindings\Geode\binding\GauntletNode.hpp bindings\bindings\Geode\binding\GauntletSelectLayer.hpp bindings\bindings\Geode\binding\GauntletSprite.hpp bindings\bindings\Geode\binding\GhostTrailEffect.hpp bindings\bindings\Geode\binding\GooglePlayDelegate.hpp bindings\bindings\Geode\binding\GooglePlayManager.hpp bindings\bindings\Geode\binding\GradientTriggerObject.hpp bindings\bindings\Geode\binding\GraphicsReloadLayer.hpp bindings\bindings\Geode\binding\GravityEffectSprite.hpp bindings\bindings\Geode\binding\GroupCommandObject2.hpp bindings\bindings\Geode\binding\HSVLiveOverlay.hpp bindings\bindings\Geode\binding\HSVWidgetDelegate.hpp bindings\bindings\Geode\binding\HSVWidgetPopup.hpp bindings\bindings\Geode\binding\HardStreak.hpp bindings\bindings\Geode\binding\InfoAlertButton.hpp bindings\bindings\Geode\binding\InfoLayer.hpp bindings\bindings\Geode\binding\InheritanceNode.hpp bindings\bindings\Geode\binding\ItemInfoPopup.hpp bindings\bindings\Geode\binding\ItemTriggerGameObject.hpp bindings\bindings\Geode\binding\KeybindingsLayer.hpp bindings\bindings\Geode\binding\KeybindingsManager.hpp bindings\bindings\Geode\binding\KeyframeAnimTriggerObject.hpp bindings\bindings\Geode\binding\KeyframeGameObject.hpp bindings\bindings\Geode\binding\KeyframeObject.hpp bindings\bindings\Geode\binding\LabelGameObject.hpp bindings\bindings\Geode\binding\LeaderboardManagerDelegate.hpp bindings\bindings\Geode\binding\LeaderboardsLayer.hpp bindings\bindings\Geode\binding\LevelAreaInnerLayer.hpp bindings\bindings\Geode\binding\LevelAreaLayer.hpp bindings\bindings\Geode\binding\LevelBrowserLayer.hpp bindings\bindings\Geode\binding\LevelCell.hpp bindings\bindings\Geode\binding\LevelCommentDelegate.hpp bindings\bindings\Geode\binding\LevelDeleteDelegate.hpp bindings\bindings\Geode\binding\LevelDownloadDelegate.hpp bindings\bindings\Geode\binding\LevelEditorLayer.hpp bindings\bindings\Geode\binding\LevelFeatureLayer.hpp bindings\bindings\Geode\binding\LevelInfoLayer.hpp bindings\bindings\Geode\binding\LevelLeaderboard.hpp bindings\bindings\Geode\binding\LevelListCell.hpp bindings\bindings\Geode\binding\LevelListDeleteDelegate.hpp bindings\bindings\Geode\binding\LevelListLayer.hpp bindings\bindings\Geode\binding\LevelManagerDelegate.hpp bindings\bindings\Geode\binding\LevelOptionsLayer.hpp bindings\bindings\Geode\binding\LevelOptionsLayer2.hpp bindings\bindings\Geode\binding\LevelPage.hpp bindings\bindings\Geode\binding\LevelSearchLayer.hpp bindings\bindings\Geode\binding\LevelSelectLayer.hpp bindings\bindings\Geode\binding\LevelSettingsDelegate.hpp bindings\bindings\Geode\binding\LevelSettingsLayer.hpp bindings\bindings\Geode\binding\LevelSettingsObject.hpp bindings\bindings\Geode\binding\LevelTools.hpp bindings\bindings\Geode\binding\LevelUpdateDelegate.hpp bindings\bindings\Geode\binding\LevelUploadDelegate.hpp bindings\bindings\Geode\binding\LikeItemDelegate.hpp bindings\bindings\Geode\binding\LikeItemLayer.hpp bindings\bindings\Geode\binding\ListButtonBar.hpp bindings\bindings\Geode\binding\ListButtonBarDelegate.hpp bindings\bindings\Geode\binding\ListButtonPage.hpp bindings\bindings\Geode\binding\ListCell.hpp bindings\bindings\Geode\binding\ListUploadDelegate.hpp bindings\bindings\Geode\binding\LoadingCircle.hpp bindings\bindings\Geode\binding\LoadingCircleSprite.hpp bindings\bindings\Geode\binding\LoadingLayer.hpp bindings\bindings\Geode\binding\LocalLevelManager.hpp bindings\bindings\Geode\binding\MPLobbyLayer.hpp bindings\bindings\Geode\binding\MapPackCell.hpp bindings\bindings\Geode\binding\MapSelectLayer.hpp bindings\bindings\Geode\binding\MenuGameLayer.hpp bindings\bindings\Geode\binding\MenuLayer.hpp bindings\bindings\Geode\binding\MessageListDelegate.hpp bindings\bindings\Geode\binding\MessagesProfilePage.hpp bindings\bindings\Geode\binding\MoreOptionsLayer.hpp bindings\bindings\Geode\binding\MoreSearchLayer.hpp bindings\bindings\Geode\binding\MoreVideoOptionsLayer.hpp bindings\bindings\Geode\binding\MultiTriggerPopup.hpp bindings\bindings\Geode\binding\MultilineBitmapFont.hpp bindings\bindings\Geode\binding\MultiplayerLayer.hpp bindings\bindings\Geode\binding\MusicArtistObject.hpp bindings\bindings\Geode\binding\MusicBrowser.hpp bindings\bindings\Geode\binding\MusicBrowserDelegate.hpp bindings\bindings\Geode\binding\MusicDelegateHandler.hpp bindings\bindings\Geode\binding\MusicDownloadDelegate.hpp bindings\bindings\Geode\binding\MusicDownloadManager.hpp bindings\bindings\Geode\binding\MusicSearchResult.hpp bindings\bindings\Geode\binding\NCSInfoLayer.hpp bindings\bindings\Geode\binding\NewgroundsInfoLayer.hpp bindings\bindings\Geode\binding\NodePoint.hpp bindings\bindings\Geode\binding\NumberInputDelegate.hpp bindings\bindings\Geode\binding\NumberInputLayer.hpp bindings\bindings\Geode\binding\OBB2D.hpp bindings\bindings\Geode\binding\ObjectControlGameObject.hpp bindings\bindings\Geode\binding\ObjectManager.hpp bindings\bindings\Geode\binding\ObjectToolbox.hpp bindings\bindings\Geode\binding\OnlineListDelegate.hpp bindings\bindings\Geode\binding\OpacityEffectAction.hpp bindings\bindings\Geode\binding\OptionsCell.hpp bindings\bindings\Geode\binding\OptionsLayer.hpp bindings\bindings\Geode\binding\OptionsObject.hpp bindings\bindings\Geode\binding\OptionsObjectDelegate.hpp bindings\bindings\Geode\binding\OptionsScrollLayer.hpp bindings\bindings\Geode\binding\ParentalOptionsLayer.hpp bindings\bindings\Geode\binding\ParticleGameObject.hpp bindings\bindings\Geode\binding\ParticlePreviewLayer.hpp bindings\bindings\Geode\binding\PauseLayer.hpp bindings\bindings\Geode\binding\PlatformDownloadDelegate.hpp bindings\bindings\Geode\binding\PlatformToolbox.hpp bindings\bindings\Geode\binding\PlayLayer.hpp bindings\bindings\Geode\binding\PlayerButtonCommand.hpp bindings\bindings\Geode\binding\PlayerCheckpoint.hpp bindings\bindings\Geode\binding\PlayerControlGameObject.hpp bindings\bindings\Geode\binding\PlayerFireBoostSprite.hpp bindings\bindings\Geode\binding\PlayerObject.hpp bindings\bindings\Geode\binding\PointNode.hpp bindings\bindings\Geode\binding\PriceLabel.hpp bindings\bindings\Geode\binding\ProfilePage.hpp bindings\bindings\Geode\binding\PromoInterstitial.hpp bindings\bindings\Geode\binding\PulseEffectAction.hpp bindings\bindings\Geode\binding\PurchaseItemPopup.hpp bindings\bindings\Geode\binding\RandTriggerGameObject.hpp bindings\bindings\Geode\binding\RateDemonLayer.hpp bindings\bindings\Geode\binding\RateLevelDelegate.hpp bindings\bindings\Geode\binding\RateLevelLayer.hpp bindings\bindings\Geode\binding\RateStarsLayer.hpp bindings\bindings\Geode\binding\RetryLevelLayer.hpp bindings\bindings\Geode\binding\RewardUnlockLayer.hpp bindings\bindings\Geode\binding\RewardedVideoDelegate.hpp bindings\bindings\Geode\binding\RewardsPage.hpp bindings\bindings\Geode\binding\RingObject.hpp bindings\bindings\Geode\binding\RotateGameplayGameObject.hpp bindings\bindings\Geode\binding\SFXBrowser.hpp bindings\bindings\Geode\binding\SFXBrowserDelegate.hpp bindings\bindings\Geode\binding\SFXFolderObject.hpp bindings\bindings\Geode\binding\SFXInfoObject.hpp bindings\bindings\Geode\binding\SFXSearchResult.hpp bindings\bindings\Geode\binding\SFXStateContainer.hpp bindings\bindings\Geode\binding\SFXTriggerGameObject.hpp bindings\bindings\Geode\binding\SFXTriggerInstance.hpp bindings\bindings\Geode\binding\SFXTriggerState.hpp bindings\bindings\Geode\binding\SavedActiveObjectState.hpp bindings\bindings\Geode\binding\SavedObjectStateRef.hpp bindings\bindings\Geode\binding\SavedSpecialObjectState.hpp bindings\bindings\Geode\binding\ScrollingLayer.hpp bindings\bindings\Geode\binding\SearchButton.hpp bindings\bindings\Geode\binding\SearchSFXPopup.hpp bindings\bindings\Geode\binding\SecretGame01Layer.hpp bindings\bindings\Geode\binding\SecretLayer.hpp bindings\bindings\Geode\binding\SecretLayer2.hpp bindings\bindings\Geode\binding\SecretLayer3.hpp bindings\bindings\Geode\binding\SecretLayer4.hpp bindings\bindings\Geode\binding\SecretLayer5.hpp bindings\bindings\Geode\binding\SecretLayer6.hpp bindings\bindings\Geode\binding\SecretNumberLayer.hpp bindings\bindings\Geode\binding\SecretRewardsLayer.hpp bindings\bindings\Geode\binding\SelectArtDelegate.hpp bindings\bindings\Geode\binding\SelectArtLayer.hpp bindings\bindings\Geode\binding\SelectEventLayer.hpp bindings\bindings\Geode\binding\SelectFontLayer.hpp bindings\bindings\Geode\binding\SelectListIconDelegate.hpp bindings\bindings\Geode\binding\SelectListIconLayer.hpp bindings\bindings\Geode\binding\SelectPremadeDelegate.hpp bindings\bindings\Geode\binding\SelectPremadeLayer.hpp bindings\bindings\Geode\binding\SelectSFXSortDelegate.hpp bindings\bindings\Geode\binding\SelectSFXSortLayer.hpp bindings\bindings\Geode\binding\SelectSettingDelegate.hpp bindings\bindings\Geode\binding\SelectSettingLayer.hpp bindings\bindings\Geode\binding\SequenceTriggerGameObject.hpp bindings\bindings\Geode\binding\SequenceTriggerState.hpp bindings\bindings\Geode\binding\SetColorIDPopup.hpp bindings\bindings\Geode\binding\SetFolderPopup.hpp bindings\bindings\Geode\binding\SetGroupIDLayer.hpp bindings\bindings\Geode\binding\SetIDPopup.hpp bindings\bindings\Geode\binding\SetIDPopupDelegate.hpp bindings\bindings\Geode\binding\SetItemIDLayer.hpp bindings\bindings\Geode\binding\SetLevelOrderPopup.hpp bindings\bindings\Geode\binding\SetTargetIDLayer.hpp bindings\bindings\Geode\binding\SetTextPopup.hpp bindings\bindings\Geode\binding\SetTextPopupDelegate.hpp bindings\bindings\Geode\binding\SetupAdvFollowEditPhysicsPopup.hpp bindings\bindings\Geode\binding\SetupAdvFollowPopup.hpp bindings\bindings\Geode\binding\SetupAdvFollowRetargetPopup.hpp bindings\bindings\Geode\binding\SetupAnimSettingsPopup.hpp bindings\bindings\Geode\binding\SetupAnimationPopup.hpp bindings\bindings\Geode\binding\SetupAreaAnimTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaFadeTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaMoveTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaRotateTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaTintTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaTransformTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaTriggerPopup.hpp bindings\bindings\Geode\binding\SetupArtSwitchPopup.hpp bindings\bindings\Geode\binding\SetupAudioLineGuidePopup.hpp bindings\bindings\Geode\binding\SetupAudioTriggerPopup.hpp bindings\bindings\Geode\binding\SetupBGSpeedTrigger.hpp bindings\bindings\Geode\binding\SetupCameraEdgePopup.hpp bindings\bindings\Geode\binding\SetupCameraGuidePopup.hpp bindings\bindings\Geode\binding\SetupCameraModePopup.hpp bindings\bindings\Geode\binding\SetupCameraOffsetTrigger.hpp bindings\bindings\Geode\binding\SetupCameraRotatePopup.hpp bindings\bindings\Geode\binding\SetupCameraRotatePopup2.hpp bindings\bindings\Geode\binding\SetupCheckpointPopup.hpp bindings\bindings\Geode\binding\SetupCoinLayer.hpp bindings\bindings\Geode\binding\SetupCollisionStateTriggerPopup.hpp bindings\bindings\Geode\binding\SetupCollisionTriggerPopup.hpp bindings\bindings\Geode\binding\SetupCountTriggerPopup.hpp bindings\bindings\Geode\binding\SetupDashRingPopup.hpp bindings\bindings\Geode\binding\SetupEndPopup.hpp bindings\bindings\Geode\binding\SetupEnterEffectPopup.hpp bindings\bindings\Geode\binding\SetupEnterTriggerPopup.hpp bindings\bindings\Geode\binding\SetupEventLinkPopup.hpp bindings\bindings\Geode\binding\SetupForceBlockPopup.hpp bindings\bindings\Geode\binding\SetupGameplayOffsetPopup.hpp bindings\bindings\Geode\binding\SetupGradientPopup.hpp bindings\bindings\Geode\binding\SetupGravityModPopup.hpp bindings\bindings\Geode\binding\SetupGravityTriggerPopup.hpp bindings\bindings\Geode\binding\SetupInstantCollisionTriggerPopup.hpp bindings\bindings\Geode\binding\SetupInstantCountPopup.hpp bindings\bindings\Geode\binding\SetupInteractObjectPopup.hpp bindings\bindings\Geode\binding\SetupItemCompareTriggerPopup.hpp bindings\bindings\Geode\binding\SetupItemEditTriggerPopup.hpp bindings\bindings\Geode\binding\SetupKeyframeAnimPopup.hpp bindings\bindings\Geode\binding\SetupKeyframePopup.hpp bindings\bindings\Geode\binding\SetupMGTrigger.hpp bindings\bindings\Geode\binding\SetupMoveCommandPopup.hpp bindings\bindings\Geode\binding\SetupObjectControlPopup.hpp bindings\bindings\Geode\binding\SetupObjectOptions2Popup.hpp bindings\bindings\Geode\binding\SetupObjectOptionsPopup.hpp bindings\bindings\Geode\binding\SetupObjectTogglePopup.hpp bindings\bindings\Geode\binding\SetupOpacityPopup.hpp bindings\bindings\Geode\binding\SetupOptionsTriggerPopup.hpp bindings\bindings\Geode\binding\SetupPersistentItemTriggerPopup.hpp bindings\bindings\Geode\binding\SetupPickupTriggerPopup.hpp bindings\bindings\Geode\binding\SetupPlatformerEndPopup.hpp bindings\bindings\Geode\binding\SetupPlayerControlPopup.hpp bindings\bindings\Geode\binding\SetupPortalPopup.hpp bindings\bindings\Geode\binding\SetupPulsePopup.hpp bindings\bindings\Geode\binding\SetupRandAdvTriggerPopup.hpp bindings\bindings\Geode\binding\SetupRandTriggerPopup.hpp bindings\bindings\Geode\binding\SetupResetTriggerPopup.hpp bindings\bindings\Geode\binding\SetupReverbPopup.hpp bindings\bindings\Geode\binding\SetupRotateCommandPopup.hpp bindings\bindings\Geode\binding\SetupRotateGameplayPopup.hpp bindings\bindings\Geode\binding\SetupRotatePopup.hpp bindings\bindings\Geode\binding\SetupSFXEditPopup.hpp bindings\bindings\Geode\binding\SetupSFXPopup.hpp bindings\bindings\Geode\binding\SetupSequenceTriggerPopup.hpp bindings\bindings\Geode\binding\SetupShaderEffectPopup.hpp bindings\bindings\Geode\binding\SetupShakePopup.hpp bindings\bindings\Geode\binding\SetupSmartBlockLayer.hpp bindings\bindings\Geode\binding\SetupSmartTemplateLayer.hpp bindings\bindings\Geode\binding\SetupSongTriggerPopup.hpp bindings\bindings\Geode\binding\SetupSpawnParticlePopup.hpp bindings\bindings\Geode\binding\SetupSpawnPopup.hpp bindings\bindings\Geode\binding\SetupStaticCameraPopup.hpp bindings\bindings\Geode\binding\SetupStopTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTeleportPopup.hpp bindings\bindings\Geode\binding\SetupTimeWarpPopup.hpp bindings\bindings\Geode\binding\SetupTimerControlTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTimerEventTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTimerTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTouchTogglePopup.hpp bindings\bindings\Geode\binding\SetupTransformPopup.hpp bindings\bindings\Geode\binding\SetupTriggerPopup.hpp bindings\bindings\Geode\binding\SetupZoomTriggerPopup.hpp bindings\bindings\Geode\binding\ShaderGameObject.hpp bindings\bindings\Geode\binding\ShaderLayer.hpp bindings\bindings\Geode\binding\ShardsPage.hpp bindings\bindings\Geode\binding\ShareCommentDelegate.hpp bindings\bindings\Geode\binding\ShareCommentLayer.hpp bindings\bindings\Geode\binding\ShareLevelLayer.hpp bindings\bindings\Geode\binding\ShareLevelSettingsLayer.hpp bindings\bindings\Geode\binding\ShareListLayer.hpp bindings\bindings\Geode\binding\SimpleObject.hpp bindings\bindings\Geode\binding\SimplePlayer.hpp bindings\bindings\Geode\binding\SlideInLayer.hpp bindings\bindings\Geode\binding\Slider.hpp bindings\bindings\Geode\binding\SliderDelegate.hpp bindings\bindings\Geode\binding\SliderThumb.hpp bindings\bindings\Geode\binding\SliderTouchLogic.hpp bindings\bindings\Geode\binding\SmartGameObject.hpp bindings\bindings\Geode\binding\SmartPrefabResult.hpp bindings\bindings\Geode\binding\SmartTemplateCell.hpp bindings\bindings\Geode\binding\SongCell.hpp bindings\bindings\Geode\binding\SongChannelState.hpp bindings\bindings\Geode\binding\SongInfoLayer.hpp bindings\bindings\Geode\binding\SongInfoObject.hpp bindings\bindings\Geode\binding\SongObject.hpp bindings\bindings\Geode\binding\SongOptionsLayer.hpp bindings\bindings\Geode\binding\SongPlaybackDelegate.hpp bindings\bindings\Geode\binding\SongSelectNode.hpp bindings\bindings\Geode\binding\SongTriggerGameObject.hpp bindings\bindings\Geode\binding\SongTriggerState.hpp bindings\bindings\Geode\binding\SongsLayer.hpp bindings\bindings\Geode\binding\SoundStateContainer.hpp bindings\bindings\Geode\binding\SpawnParticleGameObject.hpp bindings\bindings\Geode\binding\SpawnTriggerAction.hpp bindings\bindings\Geode\binding\SpawnTriggerGameObject.hpp bindings\bindings\Geode\binding\SpecialAnimGameObject.hpp bindings\bindings\Geode\binding\SpriteAnimationManager.hpp bindings\bindings\Geode\binding\SpriteDescription.hpp bindings\bindings\Geode\binding\SpritePartDelegate.hpp bindings\bindings\Geode\binding\Standalones.hpp bindings\bindings\Geode\binding\StarInfoPopup.hpp bindings\bindings\Geode\binding\StartPosObject.hpp bindings\bindings\Geode\binding\StatsCell.hpp bindings\bindings\Geode\binding\StatsLayer.hpp bindings\bindings\Geode\binding\StatsObject.hpp bindings\bindings\Geode\binding\SupportLayer.hpp bindings\bindings\Geode\binding\TOSPopup.hpp bindings\bindings\Geode\binding\TableView.hpp bindings\bindings\Geode\binding\TableViewCell.hpp bindings\bindings\Geode\binding\TableViewCellDelegate.hpp bindings\bindings\Geode\binding\TableViewDataSource.hpp bindings\bindings\Geode\binding\TableViewDelegate.hpp bindings\bindings\Geode\binding\TeleportPortalObject.hpp bindings\bindings\Geode\binding\TextAlertPopup.hpp bindings\bindings\Geode\binding\TextArea.hpp bindings\bindings\Geode\binding\TextAreaDelegate.hpp bindings\bindings\Geode\binding\TextGameObject.hpp bindings\bindings\Geode\binding\TextInputDelegate.hpp bindings\bindings\Geode\binding\TextStyleSection.hpp bindings\bindings\Geode\binding\TimerItem.hpp bindings\bindings\Geode\binding\TimerTriggerAction.hpp bindings\bindings\Geode\binding\TimerTriggerGameObject.hpp bindings\bindings\Geode\binding\ToggleTriggerAction.hpp bindings\bindings\Geode\binding\TopArtistsLayer.hpp bindings\bindings\Geode\binding\TouchToggleAction.hpp bindings\bindings\Geode\binding\TransformTriggerGameObject.hpp bindings\bindings\Geode\binding\TriggerControlGameObject.hpp bindings\bindings\Geode\binding\TriggerEffectDelegate.hpp bindings\bindings\Geode\binding\TutorialLayer.hpp bindings\bindings\Geode\binding\TutorialPopup.hpp bindings\bindings\Geode\binding\UIButtonConfig.hpp bindings\bindings\Geode\binding\UILayer.hpp bindings\bindings\Geode\binding\UIObjectSettingsPopup.hpp bindings\bindings\Geode\binding\UIOptionsLayer.hpp bindings\bindings\Geode\binding\UIPOptionsLayer.hpp bindings\bindings\Geode\binding\UISaveLoadLayer.hpp bindings\bindings\Geode\binding\UISettingsGameObject.hpp bindings\bindings\Geode\binding\URLCell.hpp bindings\bindings\Geode\binding\URLViewLayer.hpp bindings\bindings\Geode\binding\UndoObject.hpp bindings\bindings\Geode\binding\UpdateAccountSettingsPopup.hpp bindings\bindings\Geode\binding\UploadActionDelegate.hpp bindings\bindings\Geode\binding\UploadActionPopup.hpp bindings\bindings\Geode\binding\UploadListPopup.hpp bindings\bindings\Geode\binding\UploadMessageDelegate.hpp bindings\bindings\Geode\binding\UploadPopup.hpp bindings\bindings\Geode\binding\UploadPopupDelegate.hpp bindings\bindings\Geode\binding\UserInfoDelegate.hpp bindings\bindings\Geode\binding\UserListDelegate.hpp bindings\bindings\Geode\binding\VideoOptionsLayer.hpp bindings\bindings\Geode\binding\WorldLevelPage.hpp bindings\bindings\Geode\binding\WorldSelectLayer.hpp bindings\bindings\Geode\binding\tk_spline.hpp bindings\bindings\Geode\modify\AccountHelpLayer.hpp bindings\bindings\Geode\modify\AccountLayer.hpp bindings\bindings\Geode\modify\AccountLoginLayer.hpp bindings\bindings\Geode\modify\AccountRegisterLayer.hpp bindings\bindings\Geode\modify\AchievementBar.hpp bindings\bindings\Geode\modify\AchievementCell.hpp bindings\bindings\Geode\modify\AchievementManager.hpp bindings\bindings\Geode\modify\AchievementNotifier.hpp bindings\bindings\Geode\modify\AchievementsLayer.hpp bindings\bindings\Geode\modify\AdToolbox.hpp bindings\bindings\Geode\modify\AdvFollowSetup.hpp bindings\bindings\Geode\modify\AdvancedFollowEditObject.hpp bindings\bindings\Geode\modify\AdvancedFollowInstance.hpp bindings\bindings\Geode\modify\AdvancedFollowTriggerObject.hpp bindings\bindings\Geode\modify\AnimatedGameObject.hpp bindings\bindings\Geode\modify\AnimatedShopKeeper.hpp bindings\bindings\Geode\modify\AnimatedSpriteDelegate.hpp bindings\bindings\Geode\modify\AppDelegate.hpp bindings\bindings\Geode\modify\ArtTriggerGameObject.hpp bindings\bindings\Geode\modify\ArtistCell.hpp bindings\bindings\Geode\modify\AudioAssetsBrowser.hpp bindings\bindings\Geode\modify\AudioEffectsLayer.hpp bindings\bindings\Geode\modify\AudioLineGuideGameObject.hpp bindings\bindings\Geode\modify\BitmapFontCache.hpp bindings\bindings\Geode\modify\BonusDropdown.hpp bindings\bindings\Geode\modify\BoomListLayer.hpp bindings\bindings\Geode\modify\BoomListView.hpp bindings\bindings\Geode\modify\BoomScrollLayer.hpp bindings\bindings\Geode\modify\BoomScrollLayerDelegate.hpp bindings\bindings\Geode\modify\BrowseSmartKeyLayer.hpp bindings\bindings\Geode\modify\BrowseSmartTemplateLayer.hpp bindings\bindings\Geode\modify\ButtonPage.hpp bindings\bindings\Geode\modify\ButtonSprite.hpp bindings\bindings\Geode\modify\CAState.hpp bindings\bindings\Geode\modify\CCAction.hpp bindings\bindings\Geode\modify\CCActionCamera.hpp bindings\bindings\Geode\modify\CCActionEase.hpp bindings\bindings\Geode\modify\CCActionInstant.hpp bindings\bindings\Geode\modify\CCActionInterval.hpp bindings\bindings\Geode\modify\CCActionManager.hpp bindings\bindings\Geode\modify\CCAlertCircle.hpp bindings\bindings\Geode\modify\CCAnimate.hpp bindings\bindings\Geode\modify\CCAnimateFrameCache.hpp bindings\bindings\Geode\modify\CCAnimatedSprite.hpp bindings\bindings\Geode\modify\CCAnimation.hpp bindings\bindings\Geode\modify\CCAnimationCache.hpp bindings\bindings\Geode\modify\CCApplication.hpp bindings\bindings\Geode\modify\CCArray.hpp bindings\bindings\Geode\modify\CCBezierBy.hpp bindings\bindings\Geode\modify\CCBezierTo.hpp bindings\bindings\Geode\modify\CCBlink.hpp bindings\bindings\Geode\modify\CCBlockLayer.hpp bindings\bindings\Geode\modify\CCCallFunc.hpp bindings\bindings\Geode\modify\CCCallFuncN.hpp bindings\bindings\Geode\modify\CCCallFuncND.hpp bindings\bindings\Geode\modify\CCCallFuncO.hpp bindings\bindings\Geode\modify\CCCircleAlert.hpp bindings\bindings\Geode\modify\CCCircleWave.hpp bindings\bindings\Geode\modify\CCCircleWaveDelegate.hpp bindings\bindings\Geode\modify\CCClippingNode.hpp bindings\bindings\Geode\modify\CCConfiguration.hpp bindings\bindings\Geode\modify\CCContentLayer.hpp bindings\bindings\Geode\modify\CCContentManager.hpp bindings\bindings\Geode\modify\CCControl.hpp bindings\bindings\Geode\modify\CCControlColourPicker.hpp bindings\bindings\Geode\modify\CCControlHuePicker.hpp bindings\bindings\Geode\modify\CCControlSaturationBrightnessPicker.hpp bindings\bindings\Geode\modify\CCControlUtils.hpp bindings\bindings\Geode\modify\CCCountdown.hpp bindings\bindings\Geode\modify\CCCounterLabel.hpp bindings\bindings\Geode\modify\CCDelayTime.hpp bindings\bindings\Geode\modify\CCDictionary.hpp bindings\bindings\Geode\modify\CCDirector.hpp bindings\bindings\Geode\modify\CCDisplayLinkDirector.hpp bindings\bindings\Geode\modify\CCDrawNode.hpp bindings\bindings\Geode\modify\CCEGLView.hpp bindings\bindings\Geode\modify\CCEGLViewProtocol.hpp bindings\bindings\Geode\modify\CCEaseBackIn.hpp bindings\bindings\Geode\modify\CCEaseBackInOut.hpp bindings\bindings\Geode\modify\CCEaseBackOut.hpp bindings\bindings\Geode\modify\CCEaseBounce.hpp bindings\bindings\Geode\modify\CCEaseBounceIn.hpp bindings\bindings\Geode\modify\CCEaseBounceInOut.hpp bindings\bindings\Geode\modify\CCEaseBounceOut.hpp bindings\bindings\Geode\modify\CCEaseElastic.hpp bindings\bindings\Geode\modify\CCEaseElasticIn.hpp bindings\bindings\Geode\modify\CCEaseElasticInOut.hpp bindings\bindings\Geode\modify\CCEaseElasticOut.hpp bindings\bindings\Geode\modify\CCEaseExponentialIn.hpp bindings\bindings\Geode\modify\CCEaseExponentialInOut.hpp bindings\bindings\Geode\modify\CCEaseExponentialOut.hpp bindings\bindings\Geode\modify\CCEaseIn.hpp bindings\bindings\Geode\modify\CCEaseInOut.hpp bindings\bindings\Geode\modify\CCEaseOut.hpp bindings\bindings\Geode\modify\CCEaseRateAction.hpp bindings\bindings\Geode\modify\CCEaseSineIn.hpp bindings\bindings\Geode\modify\CCEaseSineInOut.hpp bindings\bindings\Geode\modify\CCEaseSineOut.hpp bindings\bindings\Geode\modify\CCExtenderNode.hpp bindings\bindings\Geode\modify\CCFadeIn.hpp bindings\bindings\Geode\modify\CCFadeOut.hpp bindings\bindings\Geode\modify\CCFadeTo.hpp bindings\bindings\Geode\modify\CCFileUtils.hpp bindings\bindings\Geode\modify\CCFiniteTimeAction.hpp bindings\bindings\Geode\modify\CCGLProgram.hpp bindings\bindings\Geode\modify\CCHide.hpp bindings\bindings\Geode\modify\CCHttpClient.hpp bindings\bindings\Geode\modify\CCHttpRequest.hpp bindings\bindings\Geode\modify\CCHttpResponse.hpp bindings\bindings\Geode\modify\CCIMEDelegate.hpp bindings\bindings\Geode\modify\CCIMEDispatcher.hpp bindings\bindings\Geode\modify\CCImage.hpp bindings\bindings\Geode\modify\CCIndexPath.hpp bindings\bindings\Geode\modify\CCJumpBy.hpp bindings\bindings\Geode\modify\CCJumpTo.hpp bindings\bindings\Geode\modify\CCKeyboardDispatcher.hpp bindings\bindings\Geode\modify\CCKeypadDispatcher.hpp bindings\bindings\Geode\modify\CCLabelBMFont.hpp bindings\bindings\Geode\modify\CCLabelTTF.hpp bindings\bindings\Geode\modify\CCLayer.hpp bindings\bindings\Geode\modify\CCLayerColor.hpp bindings\bindings\Geode\modify\CCLayerGradient.hpp bindings\bindings\Geode\modify\CCLayerRGBA.hpp bindings\bindings\Geode\modify\CCLightFlash.hpp bindings\bindings\Geode\modify\CCLightStrip.hpp bindings\bindings\Geode\modify\CCLightning.hpp bindings\bindings\Geode\modify\CCMenu.hpp bindings\bindings\Geode\modify\CCMenuItem.hpp bindings\bindings\Geode\modify\CCMenuItemImage.hpp bindings\bindings\Geode\modify\CCMenuItemSprite.hpp bindings\bindings\Geode\modify\CCMenuItemSpriteExtra.hpp bindings\bindings\Geode\modify\CCMenuItemToggler.hpp bindings\bindings\Geode\modify\CCMotionStreak.hpp bindings\bindings\Geode\modify\CCMouseDispatcher.hpp bindings\bindings\Geode\modify\CCMouseHandler.hpp bindings\bindings\Geode\modify\CCMoveBy.hpp bindings\bindings\Geode\modify\CCMoveCNode.hpp bindings\bindings\Geode\modify\CCMoveTo.hpp bindings\bindings\Geode\modify\CCNode.hpp bindings\bindings\Geode\modify\CCNodeContainer.hpp bindings\bindings\Geode\modify\CCNodeRGBA.hpp bindings\bindings\Geode\modify\CCObject.hpp bindings\bindings\Geode\modify\CCOrbitCamera.hpp bindings\bindings\Geode\modify\CCPartAnimSprite.hpp bindings\bindings\Geode\modify\CCParticleExplosion.hpp bindings\bindings\Geode\modify\CCParticleFire.hpp bindings\bindings\Geode\modify\CCParticleRain.hpp bindings\bindings\Geode\modify\CCParticleSnow.hpp bindings\bindings\Geode\modify\CCParticleSystem.hpp bindings\bindings\Geode\modify\CCParticleSystemQuad.hpp bindings\bindings\Geode\modify\CCPoolManager.hpp bindings\bindings\Geode\modify\CCProgressTimer.hpp bindings\bindings\Geode\modify\CCRemoveSelf.hpp bindings\bindings\Geode\modify\CCRenderTexture.hpp bindings\bindings\Geode\modify\CCRepeat.hpp bindings\bindings\Geode\modify\CCRepeatForever.hpp bindings\bindings\Geode\modify\CCRotateBy.hpp bindings\bindings\Geode\modify\CCRotateTo.hpp bindings\bindings\Geode\modify\CCScale9Sprite.hpp bindings\bindings\Geode\modify\CCScaleBy.hpp bindings\bindings\Geode\modify\CCScaleTo.hpp bindings\bindings\Geode\modify\CCScene.hpp bindings\bindings\Geode\modify\CCScheduler.hpp bindings\bindings\Geode\modify\CCScriptEngineManager.hpp bindings\bindings\Geode\modify\CCScrollLayerExt.hpp bindings\bindings\Geode\modify\CCScrollLayerExtDelegate.hpp bindings\bindings\Geode\modify\CCSequence.hpp bindings\bindings\Geode\modify\CCSet.hpp bindings\bindings\Geode\modify\CCShaderCache.hpp bindings\bindings\Geode\modify\CCShow.hpp bindings\bindings\Geode\modify\CCSkewBy.hpp bindings\bindings\Geode\modify\CCSkewTo.hpp bindings\bindings\Geode\modify\CCSpawn.hpp bindings\bindings\Geode\modify\CCSpeed.hpp bindings\bindings\Geode\modify\CCSprite.hpp bindings\bindings\Geode\modify\CCSpriteBatchNode.hpp bindings\bindings\Geode\modify\CCSpriteCOpacity.hpp bindings\bindings\Geode\modify\CCSpriteFrame.hpp bindings\bindings\Geode\modify\CCSpriteFrameCache.hpp bindings\bindings\Geode\modify\CCSpriteGrayscale.hpp bindings\bindings\Geode\modify\CCSpritePart.hpp bindings\bindings\Geode\modify\CCSpritePlus.hpp bindings\bindings\Geode\modify\CCSpriteWithHue.hpp bindings\bindings\Geode\modify\CCString.hpp bindings\bindings\Geode\modify\CCTargetedTouchHandler.hpp bindings\bindings\Geode\modify\CCTextFieldTTF.hpp bindings\bindings\Geode\modify\CCTextInputNode.hpp bindings\bindings\Geode\modify\CCTexture2D.hpp bindings\bindings\Geode\modify\CCTextureAtlas.hpp bindings\bindings\Geode\modify\CCTextureCache.hpp bindings\bindings\Geode\modify\CCTintTo.hpp bindings\bindings\Geode\modify\CCTouch.hpp bindings\bindings\Geode\modify\CCTouchDelegate.hpp bindings\bindings\Geode\modify\CCTouchDispatcher.hpp bindings\bindings\Geode\modify\CCTouchHandler.hpp bindings\bindings\Geode\modify\CCTransitionCrossFade.hpp bindings\bindings\Geode\modify\CCTransitionFade.hpp bindings\bindings\Geode\modify\CCTransitionFadeBL.hpp bindings\bindings\Geode\modify\CCTransitionFadeDown.hpp bindings\bindings\Geode\modify\CCTransitionFadeTR.hpp bindings\bindings\Geode\modify\CCTransitionFadeUp.hpp bindings\bindings\Geode\modify\CCTransitionFlipAngular.hpp bindings\bindings\Geode\modify\CCTransitionFlipX.hpp bindings\bindings\Geode\modify\CCTransitionFlipY.hpp bindings\bindings\Geode\modify\CCTransitionJumpZoom.hpp bindings\bindings\Geode\modify\CCTransitionMoveInB.hpp bindings\bindings\Geode\modify\CCTransitionMoveInL.hpp bindings\bindings\Geode\modify\CCTransitionMoveInR.hpp bindings\bindings\Geode\modify\CCTransitionMoveInT.hpp bindings\bindings\Geode\modify\CCTransitionRotoZoom.hpp bindings\bindings\Geode\modify\CCTransitionScene.hpp bindings\bindings\Geode\modify\CCTransitionShrinkGrow.hpp bindings\bindings\Geode\modify\CCTransitionSlideInB.hpp bindings\bindings\Geode\modify\CCTransitionSlideInL.hpp bindings\bindings\Geode\modify\CCTransitionSlideInR.hpp bindings\bindings\Geode\modify\CCTransitionSlideInT.hpp bindings\bindings\Geode\modify\CCTransitionSplitCols.hpp bindings\bindings\Geode\modify\CCTransitionSplitRows.hpp bindings\bindings\Geode\modify\CCTransitionTurnOffTiles.hpp bindings\bindings\Geode\modify\CCTransitionZoomFlipAngular.hpp bindings\bindings\Geode\modify\CCTransitionZoomFlipX.hpp bindings\bindings\Geode\modify\CCTransitionZoomFlipY.hpp bindings\bindings\Geode\modify\CCTurnOffTiles.hpp bindings\bindings\Geode\modify\CCURLObject.hpp bindings\bindings\Geode\modify\CCZone.hpp bindings\bindings\Geode\modify\CameraTriggerGameObject.hpp bindings\bindings\Geode\modify\ChallengeNode.hpp bindings\bindings\Geode\modify\ChallengesPage.hpp bindings\bindings\Geode\modify\ChanceObject.hpp bindings\bindings\Geode\modify\ChanceTriggerGameObject.hpp bindings\bindings\Geode\modify\Channel.hpp bindings\bindings\Geode\modify\ChannelControl.hpp bindings\bindings\Geode\modify\ChannelGroup.hpp bindings\bindings\Geode\modify\CharacterColorDelegate.hpp bindings\bindings\Geode\modify\CharacterColorPage.hpp bindings\bindings\Geode\modify\CheckpointGameObject.hpp bindings\bindings\Geode\modify\CheckpointObject.hpp bindings\bindings\Geode\modify\CollisionBlockPopup.hpp bindings\bindings\Geode\modify\CollisionTriggerAction.hpp bindings\bindings\Geode\modify\ColorAction.hpp bindings\bindings\Geode\modify\ColorAction2.hpp bindings\bindings\Geode\modify\ColorActionSprite.hpp bindings\bindings\Geode\modify\ColorChannelSprite.hpp bindings\bindings\Geode\modify\ColorSelectDelegate.hpp bindings\bindings\Geode\modify\ColorSelectLiveOverlay.hpp bindings\bindings\Geode\modify\ColorSelectPopup.hpp bindings\bindings\Geode\modify\ColorSetupDelegate.hpp bindings\bindings\Geode\modify\CommentCell.hpp bindings\bindings\Geode\modify\CommentUploadDelegate.hpp bindings\bindings\Geode\modify\CommunityCreditNode.hpp bindings\bindings\Geode\modify\CommunityCreditsPage.hpp bindings\bindings\Geode\modify\ConfigureHSVWidget.hpp bindings\bindings\Geode\modify\ConfigureValuePopup.hpp bindings\bindings\Geode\modify\ConfigureValuePopupDelegate.hpp bindings\bindings\Geode\modify\CountTriggerAction.hpp bindings\bindings\Geode\modify\CountTriggerGameObject.hpp bindings\bindings\Geode\modify\CreateGuidelinesLayer.hpp bindings\bindings\Geode\modify\CreateMenuItem.hpp bindings\bindings\Geode\modify\CreateParticlePopup.hpp bindings\bindings\Geode\modify\CreatorLayer.hpp bindings\bindings\Geode\modify\CurrencyRewardDelegate.hpp bindings\bindings\Geode\modify\CurrencyRewardLayer.hpp bindings\bindings\Geode\modify\CurrencySprite.hpp bindings\bindings\Geode\modify\CustomListView.hpp bindings\bindings\Geode\modify\CustomMusicCell.hpp bindings\bindings\Geode\modify\CustomSFXCell.hpp bindings\bindings\Geode\modify\CustomSFXDelegate.hpp bindings\bindings\Geode\modify\CustomSFXWidget.hpp bindings\bindings\Geode\modify\CustomSongCell.hpp bindings\bindings\Geode\modify\CustomSongDelegate.hpp bindings\bindings\Geode\modify\CustomSongLayer.hpp bindings\bindings\Geode\modify\CustomSongLayerDelegate.hpp bindings\bindings\Geode\modify\CustomSongWidget.hpp bindings\bindings\Geode\modify\CustomizeObjectLayer.hpp bindings\bindings\Geode\modify\CustomizeObjectSettingsPopup.hpp bindings\bindings\Geode\modify\DSP.hpp bindings\bindings\Geode\modify\DSPConnection.hpp bindings\bindings\Geode\modify\DS_Dictionary.hpp bindings\bindings\Geode\modify\DailyLevelNode.hpp bindings\bindings\Geode\modify\DailyLevelPage.hpp bindings\bindings\Geode\modify\DashRingObject.hpp bindings\bindings\Geode\modify\DelayedSpawnNode.hpp bindings\bindings\Geode\modify\DemonFilterDelegate.hpp bindings\bindings\Geode\modify\DemonFilterSelectLayer.hpp bindings\bindings\Geode\modify\DemonInfoPopup.hpp bindings\bindings\Geode\modify\DialogDelegate.hpp bindings\bindings\Geode\modify\DialogLayer.hpp bindings\bindings\Geode\modify\DialogObject.hpp bindings\bindings\Geode\modify\DownloadMessageDelegate.hpp bindings\bindings\Geode\modify\DrawGridLayer.hpp bindings\bindings\Geode\modify\DungeonBarsSprite.hpp bindings\bindings\Geode\modify\DynamicBitset.hpp bindings\bindings\Geode\modify\DynamicMoveCalculation.hpp bindings\bindings\Geode\modify\DynamicObjectAction.hpp bindings\bindings\Geode\modify\DynamicScrollDelegate.hpp bindings\bindings\Geode\modify\EditButtonBar.hpp bindings\bindings\Geode\modify\EditGameObjectPopup.hpp bindings\bindings\Geode\modify\EditLevelLayer.hpp bindings\bindings\Geode\modify\EditTriggersPopup.hpp bindings\bindings\Geode\modify\EditorOptionsLayer.hpp bindings\bindings\Geode\modify\EditorPauseLayer.hpp bindings\bindings\Geode\modify\EditorUI.hpp bindings\bindings\Geode\modify\EffectGameObject.hpp bindings\bindings\Geode\modify\EffectManagerState.hpp bindings\bindings\Geode\modify\EndLevelLayer.hpp bindings\bindings\Geode\modify\EndPortalObject.hpp bindings\bindings\Geode\modify\EndTriggerGameObject.hpp bindings\bindings\Geode\modify\EnhancedGameObject.hpp bindings\bindings\Geode\modify\EnhancedTriggerObject.hpp bindings\bindings\Geode\modify\EnterEffectAnimValue.hpp bindings\bindings\Geode\modify\EnterEffectInstance.hpp bindings\bindings\Geode\modify\EnterEffectObject.hpp bindings\bindings\Geode\modify\EventLinkTrigger.hpp bindings\bindings\Geode\modify\EventTriggerInstance.hpp bindings\bindings\Geode\modify\ExplodeItemNode.hpp bindings\bindings\Geode\modify\ExplodeItemSprite.hpp bindings\bindings\Geode\modify\ExtendedLayer.hpp bindings\bindings\Geode\modify\FLAlertLayer.hpp bindings\bindings\Geode\modify\FLAlertLayerProtocol.hpp bindings\bindings\Geode\modify\FMODAudioEngine.hpp bindings\bindings\Geode\modify\FMODAudioState.hpp bindings\bindings\Geode\modify\FMODLevelVisualizer.hpp bindings\bindings\Geode\modify\FMODMusic.hpp bindings\bindings\Geode\modify\FMODQueuedEffect.hpp bindings\bindings\Geode\modify\FMODQueuedMusic.hpp bindings\bindings\Geode\modify\FMODSound.hpp bindings\bindings\Geode\modify\FMODSoundState.hpp bindings\bindings\Geode\modify\FMODSoundTween.hpp bindings\bindings\Geode\modify\FRequestProfilePage.hpp bindings\bindings\Geode\modify\FileOperation.hpp bindings\bindings\Geode\modify\FileSaveManager.hpp bindings\bindings\Geode\modify\FindBPMLayer.hpp bindings\bindings\Geode\modify\FindObjectPopup.hpp bindings\bindings\Geode\modify\FollowRewardPage.hpp bindings\bindings\Geode\modify\FontObject.hpp bindings\bindings\Geode\modify\ForceBlockGameObject.hpp bindings\bindings\Geode\modify\FriendRequestDelegate.hpp bindings\bindings\Geode\modify\FriendRequestPopup.hpp bindings\bindings\Geode\modify\FriendsProfilePage.hpp bindings\bindings\Geode\modify\GJAccountBackupDelegate.hpp bindings\bindings\Geode\modify\GJAccountDelegate.hpp bindings\bindings\Geode\modify\GJAccountLoginDelegate.hpp bindings\bindings\Geode\modify\GJAccountManager.hpp bindings\bindings\Geode\modify\GJAccountRegisterDelegate.hpp bindings\bindings\Geode\modify\GJAccountSettingsDelegate.hpp bindings\bindings\Geode\modify\GJAccountSettingsLayer.hpp bindings\bindings\Geode\modify\GJAccountSyncDelegate.hpp bindings\bindings\Geode\modify\GJActionManager.hpp bindings\bindings\Geode\modify\GJAssetDownloadAction.hpp bindings\bindings\Geode\modify\GJBaseGameLayer.hpp bindings\bindings\Geode\modify\GJBigSprite.hpp bindings\bindings\Geode\modify\GJBigSpriteNode.hpp bindings\bindings\Geode\modify\GJChallengeDelegate.hpp bindings\bindings\Geode\modify\GJChallengeItem.hpp bindings\bindings\Geode\modify\GJChestSprite.hpp bindings\bindings\Geode\modify\GJColorSetupLayer.hpp bindings\bindings\Geode\modify\GJComment.hpp bindings\bindings\Geode\modify\GJCommentListLayer.hpp bindings\bindings\Geode\modify\GJDailyLevelDelegate.hpp bindings\bindings\Geode\modify\GJDifficultySprite.hpp bindings\bindings\Geode\modify\GJDropDownLayer.hpp bindings\bindings\Geode\modify\GJDropDownLayerDelegate.hpp bindings\bindings\Geode\modify\GJEffectManager.hpp bindings\bindings\Geode\modify\GJFlyGroundLayer.hpp bindings\bindings\Geode\modify\GJFollowCommandLayer.hpp bindings\bindings\Geode\modify\GJFriendRequest.hpp bindings\bindings\Geode\modify\GJGameLevel.hpp bindings\bindings\Geode\modify\GJGameLoadingLayer.hpp bindings\bindings\Geode\modify\GJGameState.hpp bindings\bindings\Geode\modify\GJGarageLayer.hpp bindings\bindings\Geode\modify\GJGradientLayer.hpp bindings\bindings\Geode\modify\GJGroundLayer.hpp bindings\bindings\Geode\modify\GJHttpResult.hpp bindings\bindings\Geode\modify\GJItemIcon.hpp bindings\bindings\Geode\modify\GJLevelList.hpp bindings\bindings\Geode\modify\GJLevelScoreCell.hpp bindings\bindings\Geode\modify\GJListLayer.hpp bindings\bindings\Geode\modify\GJLocalLevelScoreCell.hpp bindings\bindings\Geode\modify\GJLocalScore.hpp bindings\bindings\Geode\modify\GJMGLayer.hpp bindings\bindings\Geode\modify\GJMPDelegate.hpp bindings\bindings\Geode\modify\GJMapObject.hpp bindings\bindings\Geode\modify\GJMapPack.hpp bindings\bindings\Geode\modify\GJMessageCell.hpp bindings\bindings\Geode\modify\GJMessagePopup.hpp bindings\bindings\Geode\modify\GJMoreGamesLayer.hpp bindings\bindings\Geode\modify\GJMultiplayerManager.hpp bindings\bindings\Geode\modify\GJObjectDecoder.hpp bindings\bindings\Geode\modify\GJOnlineRewardDelegate.hpp bindings\bindings\Geode\modify\GJOptionsLayer.hpp bindings\bindings\Geode\modify\GJPFollowCommandLayer.hpp bindings\bindings\Geode\modify\GJPathPage.hpp bindings\bindings\Geode\modify\GJPathRewardPopup.hpp bindings\bindings\Geode\modify\GJPathSprite.hpp bindings\bindings\Geode\modify\GJPathsLayer.hpp bindings\bindings\Geode\modify\GJPointDouble.hpp bindings\bindings\Geode\modify\GJPromoPopup.hpp bindings\bindings\Geode\modify\GJPurchaseDelegate.hpp bindings\bindings\Geode\modify\GJRequestCell.hpp bindings\bindings\Geode\modify\GJRewardDelegate.hpp bindings\bindings\Geode\modify\GJRewardItem.hpp bindings\bindings\Geode\modify\GJRewardObject.hpp bindings\bindings\Geode\modify\GJRobotSprite.hpp bindings\bindings\Geode\modify\GJRotateCommandLayer.hpp bindings\bindings\Geode\modify\GJRotationControl.hpp bindings\bindings\Geode\modify\GJRotationControlDelegate.hpp bindings\bindings\Geode\modify\GJScaleControl.hpp bindings\bindings\Geode\modify\GJScaleControlDelegate.hpp bindings\bindings\Geode\modify\GJScoreCell.hpp bindings\bindings\Geode\modify\GJSearchObject.hpp bindings\bindings\Geode\modify\GJShaderState.hpp bindings\bindings\Geode\modify\GJShopLayer.hpp bindings\bindings\Geode\modify\GJSmartBlockPreview.hpp bindings\bindings\Geode\modify\GJSmartBlockPreviewSprite.hpp bindings\bindings\Geode\modify\GJSmartPrefab.hpp bindings\bindings\Geode\modify\GJSmartTemplate.hpp bindings\bindings\Geode\modify\GJSongBrowser.hpp bindings\bindings\Geode\modify\GJSpecialColorSelect.hpp bindings\bindings\Geode\modify\GJSpecialColorSelectDelegate.hpp bindings\bindings\Geode\modify\GJSpiderSprite.hpp bindings\bindings\Geode\modify\GJSpriteColor.hpp bindings\bindings\Geode\modify\GJStoreItem.hpp bindings\bindings\Geode\modify\GJTransformControl.hpp bindings\bindings\Geode\modify\GJTransformControlDelegate.hpp bindings\bindings\Geode\modify\GJTransformState.hpp bindings\bindings\Geode\modify\GJUINode.hpp bindings\bindings\Geode\modify\GJUnlockableItem.hpp bindings\bindings\Geode\modify\GJUserCell.hpp bindings\bindings\Geode\modify\GJUserMessage.hpp bindings\bindings\Geode\modify\GJUserScore.hpp bindings\bindings\Geode\modify\GJValueTween.hpp bindings\bindings\Geode\modify\GJWorldNode.hpp bindings\bindings\Geode\modify\GJWriteMessagePopup.hpp bindings\bindings\Geode\modify\GManager.hpp bindings\bindings\Geode\modify\GameCell.hpp bindings\bindings\Geode\modify\GameEffectsManager.hpp bindings\bindings\Geode\modify\GameLevelManager.hpp bindings\bindings\Geode\modify\GameLevelOptionsLayer.hpp bindings\bindings\Geode\modify\GameManager.hpp bindings\bindings\Geode\modify\GameObject.hpp bindings\bindings\Geode\modify\GameObjectCopy.hpp bindings\bindings\Geode\modify\GameObjectEditorState.hpp bindings\bindings\Geode\modify\GameObjectPhysics.hpp bindings\bindings\Geode\modify\GameOptionsLayer.hpp bindings\bindings\Geode\modify\GameOptionsTrigger.hpp bindings\bindings\Geode\modify\GameRateDelegate.hpp bindings\bindings\Geode\modify\GameStatsManager.hpp bindings\bindings\Geode\modify\GameToolbox.hpp bindings\bindings\Geode\modify\GauntletLayer.hpp bindings\bindings\Geode\modify\GauntletNode.hpp bindings\bindings\Geode\modify\GauntletSelectLayer.hpp bindings\bindings\Geode\modify\GauntletSprite.hpp bindings\bindings\Geode\modify\Geometry.hpp bindings\bindings\Geode\modify\GhostTrailEffect.hpp bindings\bindings\Geode\modify\GooglePlayDelegate.hpp bindings\bindings\Geode\modify\GooglePlayManager.hpp bindings\bindings\Geode\modify\GradientTriggerObject.hpp bindings\bindings\Geode\modify\GraphicsReloadLayer.hpp bindings\bindings\Geode\modify\GravityEffectSprite.hpp bindings\bindings\Geode\modify\GroupCommandObject2.hpp bindings\bindings\Geode\modify\HSVLiveOverlay.hpp bindings\bindings\Geode\modify\HSVWidgetDelegate.hpp bindings\bindings\Geode\modify\HSVWidgetPopup.hpp bindings\bindings\Geode\modify\HardStreak.hpp bindings\bindings\Geode\modify\InfoAlertButton.hpp bindings\bindings\Geode\modify\InfoLayer.hpp bindings\bindings\Geode\modify\InheritanceNode.hpp bindings\bindings\Geode\modify\ItemInfoPopup.hpp bindings\bindings\Geode\modify\ItemTriggerGameObject.hpp bindings\bindings\Geode\modify\KeybindingsLayer.hpp bindings\bindings\Geode\modify\KeybindingsManager.hpp bindings\bindings\Geode\modify\KeyframeAnimTriggerObject.hpp bindings\bindings\Geode\modify\KeyframeGameObject.hpp bindings\bindings\Geode\modify\KeyframeObject.hpp bindings\bindings\Geode\modify\LabelGameObject.hpp bindings\bindings\Geode\modify\LeaderboardManagerDelegate.hpp bindings\bindings\Geode\modify\LeaderboardsLayer.hpp bindings\bindings\Geode\modify\LevelAreaInnerLayer.hpp bindings\bindings\Geode\modify\LevelAreaLayer.hpp bindings\bindings\Geode\modify\LevelBrowserLayer.hpp bindings\bindings\Geode\modify\LevelCell.hpp bindings\bindings\Geode\modify\LevelCommentDelegate.hpp bindings\bindings\Geode\modify\LevelDeleteDelegate.hpp bindings\bindings\Geode\modify\LevelDownloadDelegate.hpp bindings\bindings\Geode\modify\LevelEditorLayer.hpp bindings\bindings\Geode\modify\LevelFeatureLayer.hpp bindings\bindings\Geode\modify\LevelInfoLayer.hpp bindings\bindings\Geode\modify\LevelLeaderboard.hpp bindings\bindings\Geode\modify\LevelListCell.hpp bindings\bindings\Geode\modify\LevelListDeleteDelegate.hpp bindings\bindings\Geode\modify\LevelListLayer.hpp bindings\bindings\Geode\modify\LevelManagerDelegate.hpp bindings\bindings\Geode\modify\LevelOptionsLayer.hpp bindings\bindings\Geode\modify\LevelOptionsLayer2.hpp bindings\bindings\Geode\modify\LevelPage.hpp bindings\bindings\Geode\modify\LevelSearchLayer.hpp bindings\bindings\Geode\modify\LevelSelectLayer.hpp bindings\bindings\Geode\modify\LevelSettingsDelegate.hpp bindings\bindings\Geode\modify\LevelSettingsLayer.hpp bindings\bindings\Geode\modify\LevelSettingsObject.hpp bindings\bindings\Geode\modify\LevelTools.hpp bindings\bindings\Geode\modify\LevelUpdateDelegate.hpp bindings\bindings\Geode\modify\LevelUploadDelegate.hpp bindings\bindings\Geode\modify\LikeItemDelegate.hpp bindings\bindings\Geode\modify\LikeItemLayer.hpp bindings\bindings\Geode\modify\ListButtonBar.hpp bindings\bindings\Geode\modify\ListButtonBarDelegate.hpp bindings\bindings\Geode\modify\ListButtonPage.hpp bindings\bindings\Geode\modify\ListCell.hpp bindings\bindings\Geode\modify\ListUploadDelegate.hpp bindings\bindings\Geode\modify\LoadingCircle.hpp bindings\bindings\Geode\modify\LoadingCircleSprite.hpp bindings\bindings\Geode\modify\LoadingLayer.hpp bindings\bindings\Geode\modify\LocalLevelManager.hpp bindings\bindings\Geode\modify\MPLobbyLayer.hpp bindings\bindings\Geode\modify\MapPackCell.hpp bindings\bindings\Geode\modify\MapSelectLayer.hpp bindings\bindings\Geode\modify\MenuGameLayer.hpp bindings\bindings\Geode\modify\MenuLayer.hpp bindings\bindings\Geode\modify\MessageListDelegate.hpp bindings\bindings\Geode\modify\MessagesProfilePage.hpp bindings\bindings\Geode\modify\MoreOptionsLayer.hpp bindings\bindings\Geode\modify\MoreSearchLayer.hpp bindings\bindings\Geode\modify\MoreVideoOptionsLayer.hpp bindings\bindings\Geode\modify\MultiTriggerPopup.hpp bindings\bindings\Geode\modify\MultilineBitmapFont.hpp bindings\bindings\Geode\modify\MultiplayerLayer.hpp bindings\bindings\Geode\modify\MusicArtistObject.hpp bindings\bindings\Geode\modify\MusicBrowser.hpp bindings\bindings\Geode\modify\MusicBrowserDelegate.hpp bindings\bindings\Geode\modify\MusicDelegateHandler.hpp bindings\bindings\Geode\modify\MusicDownloadDelegate.hpp bindings\bindings\Geode\modify\MusicDownloadManager.hpp bindings\bindings\Geode\modify\MusicSearchResult.hpp bindings\bindings\Geode\modify\NCSInfoLayer.hpp bindings\bindings\Geode\modify\NewgroundsInfoLayer.hpp bindings\bindings\Geode\modify\NodePoint.hpp bindings\bindings\Geode\modify\NumberInputDelegate.hpp bindings\bindings\Geode\modify\NumberInputLayer.hpp bindings\bindings\Geode\modify\OBB2D.hpp bindings\bindings\Geode\modify\ObjectControlGameObject.hpp bindings\bindings\Geode\modify\ObjectDecoder.hpp bindings\bindings\Geode\modify\ObjectManager.hpp bindings\bindings\Geode\modify\ObjectToolbox.hpp bindings\bindings\Geode\modify\OnlineListDelegate.hpp bindings\bindings\Geode\modify\OpacityEffectAction.hpp bindings\bindings\Geode\modify\OptionsCell.hpp bindings\bindings\Geode\modify\OptionsLayer.hpp bindings\bindings\Geode\modify\OptionsObject.hpp bindings\bindings\Geode\modify\OptionsObjectDelegate.hpp bindings\bindings\Geode\modify\OptionsScrollLayer.hpp bindings\bindings\Geode\modify\ParentalOptionsLayer.hpp bindings\bindings\Geode\modify\ParticleGameObject.hpp bindings\bindings\Geode\modify\ParticlePreviewLayer.hpp bindings\bindings\Geode\modify\PauseLayer.hpp bindings\bindings\Geode\modify\PlatformDownloadDelegate.hpp bindings\bindings\Geode\modify\PlatformToolbox.hpp bindings\bindings\Geode\modify\PlayLayer.hpp bindings\bindings\Geode\modify\PlayerButtonCommand.hpp bindings\bindings\Geode\modify\PlayerCheckpoint.hpp bindings\bindings\Geode\modify\PlayerControlGameObject.hpp bindings\bindings\Geode\modify\PlayerFireBoostSprite.hpp bindings\bindings\Geode\modify\PlayerObject.hpp bindings\bindings\Geode\modify\PointNode.hpp bindings\bindings\Geode\modify\PriceLabel.hpp bindings\bindings\Geode\modify\ProfilePage.hpp bindings\bindings\Geode\modify\PromoInterstitial.hpp bindings\bindings\Geode\modify\PulseEffectAction.hpp bindings\bindings\Geode\modify\PurchaseItemPopup.hpp bindings\bindings\Geode\modify\RandTriggerGameObject.hpp bindings\bindings\Geode\modify\RateDemonLayer.hpp bindings\bindings\Geode\modify\RateLevelDelegate.hpp bindings\bindings\Geode\modify\RateLevelLayer.hpp bindings\bindings\Geode\modify\RateStarsLayer.hpp bindings\bindings\Geode\modify\RetryLevelLayer.hpp bindings\bindings\Geode\modify\Reverb3D.hpp bindings\bindings\Geode\modify\RewardUnlockLayer.hpp bindings\bindings\Geode\modify\RewardedVideoDelegate.hpp bindings\bindings\Geode\modify\RewardsPage.hpp bindings\bindings\Geode\modify\RingObject.hpp bindings\bindings\Geode\modify\RotateGameplayGameObject.hpp bindings\bindings\Geode\modify\SFXBrowser.hpp bindings\bindings\Geode\modify\SFXBrowserDelegate.hpp bindings\bindings\Geode\modify\SFXFolderObject.hpp bindings\bindings\Geode\modify\SFXInfoObject.hpp bindings\bindings\Geode\modify\SFXSearchResult.hpp bindings\bindings\Geode\modify\SFXStateContainer.hpp bindings\bindings\Geode\modify\SFXTriggerGameObject.hpp bindings\bindings\Geode\modify\SFXTriggerInstance.hpp bindings\bindings\Geode\modify\SFXTriggerState.hpp bindings\bindings\Geode\modify\SavedActiveObjectState.hpp bindings\bindings\Geode\modify\SavedObjectStateRef.hpp bindings\bindings\Geode\modify\SavedSpecialObjectState.hpp bindings\bindings\Geode\modify\ScrollingLayer.hpp bindings\bindings\Geode\modify\SearchButton.hpp bindings\bindings\Geode\modify\SearchSFXPopup.hpp bindings\bindings\Geode\modify\SecretGame01Layer.hpp bindings\bindings\Geode\modify\SecretLayer.hpp bindings\bindings\Geode\modify\SecretLayer2.hpp bindings\bindings\Geode\modify\SecretLayer3.hpp bindings\bindings\Geode\modify\SecretLayer4.hpp bindings\bindings\Geode\modify\SecretLayer5.hpp bindings\bindings\Geode\modify\SecretLayer6.hpp bindings\bindings\Geode\modify\SecretNumberLayer.hpp bindings\bindings\Geode\modify\SecretRewardsLayer.hpp bindings\bindings\Geode\modify\SelectArtDelegate.hpp bindings\bindings\Geode\modify\SelectArtLayer.hpp bindings\bindings\Geode\modify\SelectEventLayer.hpp bindings\bindings\Geode\modify\SelectFontLayer.hpp bindings\bindings\Geode\modify\SelectListIconDelegate.hpp bindings\bindings\Geode\modify\SelectListIconLayer.hpp bindings\bindings\Geode\modify\SelectPremadeDelegate.hpp bindings\bindings\Geode\modify\SelectPremadeLayer.hpp bindings\bindings\Geode\modify\SelectSFXSortDelegate.hpp bindings\bindings\Geode\modify\SelectSFXSortLayer.hpp bindings\bindings\Geode\modify\SelectSettingDelegate.hpp bindings\bindings\Geode\modify\SelectSettingLayer.hpp bindings\bindings\Geode\modify\SequenceTriggerGameObject.hpp bindings\bindings\Geode\modify\SequenceTriggerState.hpp bindings\bindings\Geode\modify\SetColorIDPopup.hpp bindings\bindings\Geode\modify\SetFolderPopup.hpp bindings\bindings\Geode\modify\SetGroupIDLayer.hpp bindings\bindings\Geode\modify\SetIDPopup.hpp bindings\bindings\Geode\modify\SetIDPopupDelegate.hpp bindings\bindings\Geode\modify\SetItemIDLayer.hpp bindings\bindings\Geode\modify\SetLevelOrderPopup.hpp bindings\bindings\Geode\modify\SetTargetIDLayer.hpp bindings\bindings\Geode\modify\SetTextPopup.hpp bindings\bindings\Geode\modify\SetTextPopupDelegate.hpp bindings\bindings\Geode\modify\SetupAdvFollowEditPhysicsPopup.hpp bindings\bindings\Geode\modify\SetupAdvFollowPopup.hpp bindings\bindings\Geode\modify\SetupAdvFollowRetargetPopup.hpp bindings\bindings\Geode\modify\SetupAnimSettingsPopup.hpp bindings\bindings\Geode\modify\SetupAnimationPopup.hpp bindings\bindings\Geode\modify\SetupAreaAnimTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaFadeTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaMoveTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaRotateTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaTintTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaTransformTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaTriggerPopup.hpp bindings\bindings\Geode\modify\SetupArtSwitchPopup.hpp bindings\bindings\Geode\modify\SetupAudioLineGuidePopup.hpp bindings\bindings\Geode\modify\SetupAudioTriggerPopup.hpp bindings\bindings\Geode\modify\SetupBGSpeedTrigger.hpp bindings\bindings\Geode\modify\SetupCameraEdgePopup.hpp bindings\bindings\Geode\modify\SetupCameraGuidePopup.hpp bindings\bindings\Geode\modify\SetupCameraModePopup.hpp bindings\bindings\Geode\modify\SetupCameraOffsetTrigger.hpp bindings\bindings\Geode\modify\SetupCameraRotatePopup.hpp bindings\bindings\Geode\modify\SetupCameraRotatePopup2.hpp bindings\bindings\Geode\modify\SetupCheckpointPopup.hpp bindings\bindings\Geode\modify\SetupCoinLayer.hpp bindings\bindings\Geode\modify\SetupCollisionStateTriggerPopup.hpp bindings\bindings\Geode\modify\SetupCollisionTriggerPopup.hpp bindings\bindings\Geode\modify\SetupCountTriggerPopup.hpp bindings\bindings\Geode\modify\SetupDashRingPopup.hpp bindings\bindings\Geode\modify\SetupEndPopup.hpp bindings\bindings\Geode\modify\SetupEnterEffectPopup.hpp bindings\bindings\Geode\modify\SetupEnterTriggerPopup.hpp bindings\bindings\Geode\modify\SetupEventLinkPopup.hpp bindings\bindings\Geode\modify\SetupForceBlockPopup.hpp bindings\bindings\Geode\modify\SetupGameplayOffsetPopup.hpp bindings\bindings\Geode\modify\SetupGradientPopup.hpp bindings\bindings\Geode\modify\SetupGravityModPopup.hpp bindings\bindings\Geode\modify\SetupGravityTriggerPopup.hpp bindings\bindings\Geode\modify\SetupInstantCollisionTriggerPopup.hpp bindings\bindings\Geode\modify\SetupInstantCountPopup.hpp bindings\bindings\Geode\modify\SetupInteractObjectPopup.hpp bindings\bindings\Geode\modify\SetupItemCompareTriggerPopup.hpp bindings\bindings\Geode\modify\SetupItemEditTriggerPopup.hpp bindings\bindings\Geode\modify\SetupKeyframeAnimPopup.hpp bindings\bindings\Geode\modify\SetupKeyframePopup.hpp bindings\bindings\Geode\modify\SetupMGTrigger.hpp bindings\bindings\Geode\modify\SetupMoveCommandPopup.hpp bindings\bindings\Geode\modify\SetupObjectControlPopup.hpp bindings\bindings\Geode\modify\SetupObjectOptions2Popup.hpp bindings\bindings\Geode\modify\SetupObjectOptionsPopup.hpp bindings\bindings\Geode\modify\SetupObjectTogglePopup.hpp bindings\bindings\Geode\modify\SetupOpacityPopup.hpp bindings\bindings\Geode\modify\SetupOptionsTriggerPopup.hpp bindings\bindings\Geode\modify\SetupPersistentItemTriggerPopup.hpp bindings\bindings\Geode\modify\SetupPickupTriggerPopup.hpp bindings\bindings\Geode\modify\SetupPlatformerEndPopup.hpp bindings\bindings\Geode\modify\SetupPlayerControlPopup.hpp bindings\bindings\Geode\modify\SetupPortalPopup.hpp bindings\bindings\Geode\modify\SetupPulsePopup.hpp bindings\bindings\Geode\modify\SetupRandAdvTriggerPopup.hpp bindings\bindings\Geode\modify\SetupRandTriggerPopup.hpp bindings\bindings\Geode\modify\SetupResetTriggerPopup.hpp bindings\bindings\Geode\modify\SetupReverbPopup.hpp bindings\bindings\Geode\modify\SetupRotateCommandPopup.hpp bindings\bindings\Geode\modify\SetupRotateGameplayPopup.hpp bindings\bindings\Geode\modify\SetupRotatePopup.hpp bindings\bindings\Geode\modify\SetupSFXEditPopup.hpp bindings\bindings\Geode\modify\SetupSFXPopup.hpp bindings\bindings\Geode\modify\SetupSequenceTriggerPopup.hpp bindings\bindings\Geode\modify\SetupShaderEffectPopup.hpp bindings\bindings\Geode\modify\SetupShakePopup.hpp bindings\bindings\Geode\modify\SetupSmartBlockLayer.hpp bindings\bindings\Geode\modify\SetupSmartTemplateLayer.hpp bindings\bindings\Geode\modify\SetupSongTriggerPopup.hpp bindings\bindings\Geode\modify\SetupSpawnParticlePopup.hpp bindings\bindings\Geode\modify\SetupSpawnPopup.hpp bindings\bindings\Geode\modify\SetupStaticCameraPopup.hpp bindings\bindings\Geode\modify\SetupStopTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTeleportPopup.hpp bindings\bindings\Geode\modify\SetupTimeWarpPopup.hpp bindings\bindings\Geode\modify\SetupTimerControlTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTimerEventTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTimerTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTouchTogglePopup.hpp bindings\bindings\Geode\modify\SetupTransformPopup.hpp bindings\bindings\Geode\modify\SetupTriggerPopup.hpp bindings\bindings\Geode\modify\SetupZoomTriggerPopup.hpp bindings\bindings\Geode\modify\ShaderGameObject.hpp bindings\bindings\Geode\modify\ShaderLayer.hpp bindings\bindings\Geode\modify\ShardsPage.hpp bindings\bindings\Geode\modify\ShareCommentDelegate.hpp bindings\bindings\Geode\modify\ShareCommentLayer.hpp bindings\bindings\Geode\modify\ShareLevelLayer.hpp bindings\bindings\Geode\modify\ShareLevelSettingsLayer.hpp bindings\bindings\Geode\modify\ShareListLayer.hpp bindings\bindings\Geode\modify\SimpleObject.hpp bindings\bindings\Geode\modify\SimplePlayer.hpp bindings\bindings\Geode\modify\SlideInLayer.hpp bindings\bindings\Geode\modify\Slider.hpp bindings\bindings\Geode\modify\SliderDelegate.hpp bindings\bindings\Geode\modify\SliderThumb.hpp bindings\bindings\Geode\modify\SliderTouchLogic.hpp bindings\bindings\Geode\modify\SmartGameObject.hpp bindings\bindings\Geode\modify\SmartPrefabResult.hpp bindings\bindings\Geode\modify\SmartTemplateCell.hpp bindings\bindings\Geode\modify\SongCell.hpp bindings\bindings\Geode\modify\SongChannelState.hpp bindings\bindings\Geode\modify\SongInfoLayer.hpp bindings\bindings\Geode\modify\SongInfoObject.hpp bindings\bindings\Geode\modify\SongObject.hpp bindings\bindings\Geode\modify\SongOptionsLayer.hpp bindings\bindings\Geode\modify\SongPlaybackDelegate.hpp bindings\bindings\Geode\modify\SongSelectNode.hpp bindings\bindings\Geode\modify\SongTriggerGameObject.hpp bindings\bindings\Geode\modify\SongTriggerState.hpp bindings\bindings\Geode\modify\SongsLayer.hpp bindings\bindings\Geode\modify\Sound.hpp bindings\bindings\Geode\modify\SoundGroup.hpp bindings\bindings\Geode\modify\SoundStateContainer.hpp bindings\bindings\Geode\modify\SpawnParticleGameObject.hpp bindings\bindings\Geode\modify\SpawnTriggerAction.hpp bindings\bindings\Geode\modify\SpawnTriggerGameObject.hpp bindings\bindings\Geode\modify\SpecialAnimGameObject.hpp bindings\bindings\Geode\modify\SpriteAnimationManager.hpp bindings\bindings\Geode\modify\SpriteDescription.hpp bindings\bindings\Geode\modify\SpritePartDelegate.hpp bindings\bindings\Geode\modify\StarInfoPopup.hpp bindings\bindings\Geode\modify\StartPosObject.hpp bindings\bindings\Geode\modify\StatsCell.hpp bindings\bindings\Geode\modify\StatsLayer.hpp bindings\bindings\Geode\modify\StatsObject.hpp bindings\bindings\Geode\modify\SupportLayer.hpp bindings\bindings\Geode\modify\System.hpp bindings\bindings\Geode\modify\TOSPopup.hpp bindings\bindings\Geode\modify\TableView.hpp bindings\bindings\Geode\modify\TableViewCell.hpp bindings\bindings\Geode\modify\TableViewCellDelegate.hpp bindings\bindings\Geode\modify\TableViewDataSource.hpp bindings\bindings\Geode\modify\TableViewDelegate.hpp bindings\bindings\Geode\modify\TeleportPortalObject.hpp bindings\bindings\Geode\modify\TextAlertPopup.hpp bindings\bindings\Geode\modify\TextArea.hpp bindings\bindings\Geode\modify\TextAreaDelegate.hpp bindings\bindings\Geode\modify\TextGameObject.hpp bindings\bindings\Geode\modify\TextInputDelegate.hpp bindings\bindings\Geode\modify\TextStyleSection.hpp bindings\bindings\Geode\modify\TimerItem.hpp bindings\bindings\Geode\modify\TimerTriggerAction.hpp bindings\bindings\Geode\modify\TimerTriggerGameObject.hpp bindings\bindings\Geode\modify\ToggleTriggerAction.hpp bindings\bindings\Geode\modify\TopArtistsLayer.hpp bindings\bindings\Geode\modify\TouchToggleAction.hpp bindings\bindings\Geode\modify\TransformTriggerGameObject.hpp bindings\bindings\Geode\modify\TriggerControlGameObject.hpp bindings\bindings\Geode\modify\TriggerEffectDelegate.hpp bindings\bindings\Geode\modify\TutorialLayer.hpp bindings\bindings\Geode\modify\TutorialPopup.hpp bindings\bindings\Geode\modify\UIButtonConfig.hpp bindings\bindings\Geode\modify\UILayer.hpp bindings\bindings\Geode\modify\UIObjectSettingsPopup.hpp bindings\bindings\Geode\modify\UIOptionsLayer.hpp bindings\bindings\Geode\modify\UIPOptionsLayer.hpp bindings\bindings\Geode\modify\UISaveLoadLayer.hpp bindings\bindings\Geode\modify\UISettingsGameObject.hpp bindings\bindings\Geode\modify\URLCell.hpp bindings\bindings\Geode\modify\URLViewLayer.hpp bindings\bindings\Geode\modify\UndoObject.hpp bindings\bindings\Geode\modify\UpdateAccountSettingsPopup.hpp bindings\bindings\Geode\modify\UploadActionDelegate.hpp bindings\bindings\Geode\modify\UploadActionPopup.hpp bindings\bindings\Geode\modify\UploadListPopup.hpp bindings\bindings\Geode\modify\UploadMessageDelegate.hpp bindings\bindings\Geode\modify\UploadPopup.hpp bindings\bindings\Geode\modify\UploadPopupDelegate.hpp bindings\bindings\Geode\modify\UserInfoDelegate.hpp bindings\bindings\Geode\modify\UserListDelegate.hpp bindings\bindings\Geode\modify\VideoOptionsLayer.hpp bindings\bindings\Geode\modify\WorldLevelPage.hpp bindings\bindings\Geode\modify\WorldSelectLayer.hpp bindings\bindings\Geode\modify\ZipUtils.hpp bindings\bindings\Geode\modify\tk_spline.hpp bindings\bindings\Geode\modify\xml_document.hpp bindings\bindings\Geode\modify\xml_node.hpp cmake_object_order_depends_target_fmt

build bindings\CMakeFiles\GeodeBindings.dir\bindings\Geode\GeneratedSource.cpp.obj: CXX_COMPILER__GeodeBindings_unscanned_Debug C$:\Users\fg906\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings\Geode\GeneratedSource.cpp || cmake_object_order_depends_target_GeodeBindings
  DEFINES = -DGEODE_USE_NEW_DESTRUCTOR_LOCK=1 -D_HAS_ITERATOR_DEBUGGING=0
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /utf-8
  INCLUDES = -IC:\Users\<USER>\mod-dev\loader\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\include -IC:\Users\<USER>\mod-dev\loader\include\Geode\cocos\extensions -IC:\Users\<USER>\mod-dev\loader\include\Geode\fmod -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\bindings -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\bindings-src\bindings\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\fmt-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\tuliphook-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\result-src\include -external:IC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\_deps\json-src\include -external:W0
  OBJECT_DIR = bindings\CMakeFiles\GeodeBindings.dir
  OBJECT_FILE_DIR = bindings\CMakeFiles\GeodeBindings.dir\bindings\Geode
  TARGET_COMPILE_PDB = bindings\CMakeFiles\GeodeBindings.dir\GeodeBindings.pdb
  TARGET_PDB = bindings\GeodeBindings.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GeodeBindings


#############################################
# Link the static library bindings\GeodeBindings.lib

build bindings\GeodeBindings.lib: CXX_STATIC_LIBRARY_LINKER__GeodeBindings_Debug bindings\CMakeFiles\GeodeBindings.dir\bindings\Geode\GeneratedSource.cpp.obj || _deps\fmt-build\fmtd.lib
  LANGUAGE_COMPILE_FLAGS = /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64
  OBJECT_DIR = bindings\CMakeFiles\GeodeBindings.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = bindings\CMakeFiles\GeodeBindings.dir\GeodeBindings.pdb
  TARGET_FILE = bindings\GeodeBindings.lib
  TARGET_PDB = bindings\GeodeBindings.pdb


#############################################
# Utility command for edit_cache

build bindings\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build bindings\edit_cache: phony bindings\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build bindings\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\source\repos\Paibotgeode -BC:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build bindings\rebuild_cache: phony bindings\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build bindings\list_install_components: phony


#############################################
# Utility command for install

build bindings\CMakeFiles\install.util: CUSTOM_COMMAND bindings\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build bindings\install: phony bindings\CMakeFiles\install.util


#############################################
# Utility command for install/local

build bindings\CMakeFiles\install\local.util: CUSTOM_COMMAND bindings\all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build bindings\install\local: phony bindings\CMakeFiles\install\local.util


#############################################
# Custom command for bindings\bindings\Geode\CodegenData.txt

build bindings\bindings\Geode\CodegenData.txt bindings\bindings\Geode\GeneratedBinding.hpp bindings\bindings\Geode\GeneratedModify.hpp bindings\bindings\Geode\GeneratedPredeclare.hpp bindings\bindings\Geode\GeneratedSource.cpp bindings\bindings\Geode\binding\AccountHelpLayer.hpp bindings\bindings\Geode\binding\AccountLayer.hpp bindings\bindings\Geode\binding\AccountLoginLayer.hpp bindings\bindings\Geode\binding\AccountRegisterLayer.hpp bindings\bindings\Geode\binding\AchievementBar.hpp bindings\bindings\Geode\binding\AchievementCell.hpp bindings\bindings\Geode\binding\AchievementManager.hpp bindings\bindings\Geode\binding\AchievementNotifier.hpp bindings\bindings\Geode\binding\AchievementsLayer.hpp bindings\bindings\Geode\binding\AdToolbox.hpp bindings\bindings\Geode\binding\AdvFollowSetup.hpp bindings\bindings\Geode\binding\AdvancedFollowEditObject.hpp bindings\bindings\Geode\binding\AdvancedFollowInstance.hpp bindings\bindings\Geode\binding\AdvancedFollowTriggerObject.hpp bindings\bindings\Geode\binding\AnimatedGameObject.hpp bindings\bindings\Geode\binding\AnimatedShopKeeper.hpp bindings\bindings\Geode\binding\AnimatedSpriteDelegate.hpp bindings\bindings\Geode\binding\AppDelegate.hpp bindings\bindings\Geode\binding\ArtTriggerGameObject.hpp bindings\bindings\Geode\binding\ArtistCell.hpp bindings\bindings\Geode\binding\AudioAssetsBrowser.hpp bindings\bindings\Geode\binding\AudioEffectsLayer.hpp bindings\bindings\Geode\binding\AudioLineGuideGameObject.hpp bindings\bindings\Geode\binding\BitmapFontCache.hpp bindings\bindings\Geode\binding\BonusDropdown.hpp bindings\bindings\Geode\binding\BoomListLayer.hpp bindings\bindings\Geode\binding\BoomListView.hpp bindings\bindings\Geode\binding\BoomScrollLayer.hpp bindings\bindings\Geode\binding\BoomScrollLayerDelegate.hpp bindings\bindings\Geode\binding\BrowseSmartKeyLayer.hpp bindings\bindings\Geode\binding\BrowseSmartTemplateLayer.hpp bindings\bindings\Geode\binding\ButtonPage.hpp bindings\bindings\Geode\binding\ButtonSprite.hpp bindings\bindings\Geode\binding\CAState.hpp bindings\bindings\Geode\binding\CCAlertCircle.hpp bindings\bindings\Geode\binding\CCAnimateFrameCache.hpp bindings\bindings\Geode\binding\CCAnimatedSprite.hpp bindings\bindings\Geode\binding\CCBlockLayer.hpp bindings\bindings\Geode\binding\CCCircleAlert.hpp bindings\bindings\Geode\binding\CCCircleWave.hpp bindings\bindings\Geode\binding\CCCircleWaveDelegate.hpp bindings\bindings\Geode\binding\CCContentLayer.hpp bindings\bindings\Geode\binding\CCCountdown.hpp bindings\bindings\Geode\binding\CCCounterLabel.hpp bindings\bindings\Geode\binding\CCExtenderNode.hpp bindings\bindings\Geode\binding\CCIndexPath.hpp bindings\bindings\Geode\binding\CCLightFlash.hpp bindings\bindings\Geode\binding\CCLightStrip.hpp bindings\bindings\Geode\binding\CCMenuItemSpriteExtra.hpp bindings\bindings\Geode\binding\CCMenuItemToggler.hpp bindings\bindings\Geode\binding\CCMoveCNode.hpp bindings\bindings\Geode\binding\CCNodeContainer.hpp bindings\bindings\Geode\binding\CCPartAnimSprite.hpp bindings\bindings\Geode\binding\CCScrollLayerExt.hpp bindings\bindings\Geode\binding\CCScrollLayerExtDelegate.hpp bindings\bindings\Geode\binding\CCSpriteCOpacity.hpp bindings\bindings\Geode\binding\CCSpriteGrayscale.hpp bindings\bindings\Geode\binding\CCSpritePart.hpp bindings\bindings\Geode\binding\CCSpritePlus.hpp bindings\bindings\Geode\binding\CCSpriteWithHue.hpp bindings\bindings\Geode\binding\CCTextInputNode.hpp bindings\bindings\Geode\binding\CCURLObject.hpp bindings\bindings\Geode\binding\CameraTriggerGameObject.hpp bindings\bindings\Geode\binding\ChallengeNode.hpp bindings\bindings\Geode\binding\ChallengesPage.hpp bindings\bindings\Geode\binding\ChanceObject.hpp bindings\bindings\Geode\binding\ChanceTriggerGameObject.hpp bindings\bindings\Geode\binding\CharacterColorDelegate.hpp bindings\bindings\Geode\binding\CharacterColorPage.hpp bindings\bindings\Geode\binding\CheckpointGameObject.hpp bindings\bindings\Geode\binding\CheckpointObject.hpp bindings\bindings\Geode\binding\CollisionBlockPopup.hpp bindings\bindings\Geode\binding\CollisionTriggerAction.hpp bindings\bindings\Geode\binding\ColorAction.hpp bindings\bindings\Geode\binding\ColorAction2.hpp bindings\bindings\Geode\binding\ColorActionSprite.hpp bindings\bindings\Geode\binding\ColorChannelSprite.hpp bindings\bindings\Geode\binding\ColorSelectDelegate.hpp bindings\bindings\Geode\binding\ColorSelectLiveOverlay.hpp bindings\bindings\Geode\binding\ColorSelectPopup.hpp bindings\bindings\Geode\binding\ColorSetupDelegate.hpp bindings\bindings\Geode\binding\CommentCell.hpp bindings\bindings\Geode\binding\CommentUploadDelegate.hpp bindings\bindings\Geode\binding\CommunityCreditNode.hpp bindings\bindings\Geode\binding\CommunityCreditsPage.hpp bindings\bindings\Geode\binding\ConfigureHSVWidget.hpp bindings\bindings\Geode\binding\ConfigureValuePopup.hpp bindings\bindings\Geode\binding\ConfigureValuePopupDelegate.hpp bindings\bindings\Geode\binding\CountTriggerAction.hpp bindings\bindings\Geode\binding\CountTriggerGameObject.hpp bindings\bindings\Geode\binding\CreateGuidelinesLayer.hpp bindings\bindings\Geode\binding\CreateMenuItem.hpp bindings\bindings\Geode\binding\CreateParticlePopup.hpp bindings\bindings\Geode\binding\CreatorLayer.hpp bindings\bindings\Geode\binding\CurrencyRewardDelegate.hpp bindings\bindings\Geode\binding\CurrencyRewardLayer.hpp bindings\bindings\Geode\binding\CurrencySprite.hpp bindings\bindings\Geode\binding\CustomListView.hpp bindings\bindings\Geode\binding\CustomMusicCell.hpp bindings\bindings\Geode\binding\CustomSFXCell.hpp bindings\bindings\Geode\binding\CustomSFXDelegate.hpp bindings\bindings\Geode\binding\CustomSFXWidget.hpp bindings\bindings\Geode\binding\CustomSongCell.hpp bindings\bindings\Geode\binding\CustomSongDelegate.hpp bindings\bindings\Geode\binding\CustomSongLayer.hpp bindings\bindings\Geode\binding\CustomSongLayerDelegate.hpp bindings\bindings\Geode\binding\CustomSongWidget.hpp bindings\bindings\Geode\binding\CustomizeObjectLayer.hpp bindings\bindings\Geode\binding\CustomizeObjectSettingsPopup.hpp bindings\bindings\Geode\binding\DailyLevelNode.hpp bindings\bindings\Geode\binding\DailyLevelPage.hpp bindings\bindings\Geode\binding\DashRingObject.hpp bindings\bindings\Geode\binding\DelayedSpawnNode.hpp bindings\bindings\Geode\binding\DemonFilterDelegate.hpp bindings\bindings\Geode\binding\DemonFilterSelectLayer.hpp bindings\bindings\Geode\binding\DemonInfoPopup.hpp bindings\bindings\Geode\binding\DialogDelegate.hpp bindings\bindings\Geode\binding\DialogLayer.hpp bindings\bindings\Geode\binding\DialogObject.hpp bindings\bindings\Geode\binding\DownloadMessageDelegate.hpp bindings\bindings\Geode\binding\DrawGridLayer.hpp bindings\bindings\Geode\binding\DungeonBarsSprite.hpp bindings\bindings\Geode\binding\DynamicBitset.hpp bindings\bindings\Geode\binding\DynamicMoveCalculation.hpp bindings\bindings\Geode\binding\DynamicObjectAction.hpp bindings\bindings\Geode\binding\DynamicScrollDelegate.hpp bindings\bindings\Geode\binding\EditButtonBar.hpp bindings\bindings\Geode\binding\EditGameObjectPopup.hpp bindings\bindings\Geode\binding\EditLevelLayer.hpp bindings\bindings\Geode\binding\EditTriggersPopup.hpp bindings\bindings\Geode\binding\EditorOptionsLayer.hpp bindings\bindings\Geode\binding\EditorPauseLayer.hpp bindings\bindings\Geode\binding\EditorUI.hpp bindings\bindings\Geode\binding\EffectGameObject.hpp bindings\bindings\Geode\binding\EffectManagerState.hpp bindings\bindings\Geode\binding\EndLevelLayer.hpp bindings\bindings\Geode\binding\EndPortalObject.hpp bindings\bindings\Geode\binding\EndTriggerGameObject.hpp bindings\bindings\Geode\binding\EnhancedGameObject.hpp bindings\bindings\Geode\binding\EnhancedTriggerObject.hpp bindings\bindings\Geode\binding\EnterEffectAnimValue.hpp bindings\bindings\Geode\binding\EnterEffectInstance.hpp bindings\bindings\Geode\binding\EnterEffectObject.hpp bindings\bindings\Geode\binding\EventLinkTrigger.hpp bindings\bindings\Geode\binding\EventTriggerInstance.hpp bindings\bindings\Geode\binding\ExplodeItemNode.hpp bindings\bindings\Geode\binding\ExplodeItemSprite.hpp bindings\bindings\Geode\binding\ExtendedLayer.hpp bindings\bindings\Geode\binding\FLAlertLayer.hpp bindings\bindings\Geode\binding\FLAlertLayerProtocol.hpp bindings\bindings\Geode\binding\FMODAudioEngine.hpp bindings\bindings\Geode\binding\FMODAudioState.hpp bindings\bindings\Geode\binding\FMODLevelVisualizer.hpp bindings\bindings\Geode\binding\FMODMusic.hpp bindings\bindings\Geode\binding\FMODQueuedEffect.hpp bindings\bindings\Geode\binding\FMODQueuedMusic.hpp bindings\bindings\Geode\binding\FMODSound.hpp bindings\bindings\Geode\binding\FMODSoundState.hpp bindings\bindings\Geode\binding\FMODSoundTween.hpp bindings\bindings\Geode\binding\FRequestProfilePage.hpp bindings\bindings\Geode\binding\FileOperation.hpp bindings\bindings\Geode\binding\FileSaveManager.hpp bindings\bindings\Geode\binding\FindBPMLayer.hpp bindings\bindings\Geode\binding\FindObjectPopup.hpp bindings\bindings\Geode\binding\FollowRewardPage.hpp bindings\bindings\Geode\binding\FontObject.hpp bindings\bindings\Geode\binding\ForceBlockGameObject.hpp bindings\bindings\Geode\binding\FriendRequestDelegate.hpp bindings\bindings\Geode\binding\FriendRequestPopup.hpp bindings\bindings\Geode\binding\FriendsProfilePage.hpp bindings\bindings\Geode\binding\GJAccountBackupDelegate.hpp bindings\bindings\Geode\binding\GJAccountDelegate.hpp bindings\bindings\Geode\binding\GJAccountLoginDelegate.hpp bindings\bindings\Geode\binding\GJAccountManager.hpp bindings\bindings\Geode\binding\GJAccountRegisterDelegate.hpp bindings\bindings\Geode\binding\GJAccountSettingsDelegate.hpp bindings\bindings\Geode\binding\GJAccountSettingsLayer.hpp bindings\bindings\Geode\binding\GJAccountSyncDelegate.hpp bindings\bindings\Geode\binding\GJActionManager.hpp bindings\bindings\Geode\binding\GJAssetDownloadAction.hpp bindings\bindings\Geode\binding\GJBaseGameLayer.hpp bindings\bindings\Geode\binding\GJBigSprite.hpp bindings\bindings\Geode\binding\GJBigSpriteNode.hpp bindings\bindings\Geode\binding\GJChallengeDelegate.hpp bindings\bindings\Geode\binding\GJChallengeItem.hpp bindings\bindings\Geode\binding\GJChestSprite.hpp bindings\bindings\Geode\binding\GJColorSetupLayer.hpp bindings\bindings\Geode\binding\GJComment.hpp bindings\bindings\Geode\binding\GJCommentListLayer.hpp bindings\bindings\Geode\binding\GJDailyLevelDelegate.hpp bindings\bindings\Geode\binding\GJDifficultySprite.hpp bindings\bindings\Geode\binding\GJDropDownLayer.hpp bindings\bindings\Geode\binding\GJDropDownLayerDelegate.hpp bindings\bindings\Geode\binding\GJEffectManager.hpp bindings\bindings\Geode\binding\GJFlyGroundLayer.hpp bindings\bindings\Geode\binding\GJFollowCommandLayer.hpp bindings\bindings\Geode\binding\GJFriendRequest.hpp bindings\bindings\Geode\binding\GJGameLevel.hpp bindings\bindings\Geode\binding\GJGameLoadingLayer.hpp bindings\bindings\Geode\binding\GJGameState.hpp bindings\bindings\Geode\binding\GJGarageLayer.hpp bindings\bindings\Geode\binding\GJGradientLayer.hpp bindings\bindings\Geode\binding\GJGroundLayer.hpp bindings\bindings\Geode\binding\GJHttpResult.hpp bindings\bindings\Geode\binding\GJItemIcon.hpp bindings\bindings\Geode\binding\GJLevelList.hpp bindings\bindings\Geode\binding\GJLevelScoreCell.hpp bindings\bindings\Geode\binding\GJListLayer.hpp bindings\bindings\Geode\binding\GJLocalLevelScoreCell.hpp bindings\bindings\Geode\binding\GJLocalScore.hpp bindings\bindings\Geode\binding\GJMGLayer.hpp bindings\bindings\Geode\binding\GJMPDelegate.hpp bindings\bindings\Geode\binding\GJMapObject.hpp bindings\bindings\Geode\binding\GJMapPack.hpp bindings\bindings\Geode\binding\GJMessageCell.hpp bindings\bindings\Geode\binding\GJMessagePopup.hpp bindings\bindings\Geode\binding\GJMoreGamesLayer.hpp bindings\bindings\Geode\binding\GJMultiplayerManager.hpp bindings\bindings\Geode\binding\GJObjectDecoder.hpp bindings\bindings\Geode\binding\GJOnlineRewardDelegate.hpp bindings\bindings\Geode\binding\GJOptionsLayer.hpp bindings\bindings\Geode\binding\GJPFollowCommandLayer.hpp bindings\bindings\Geode\binding\GJPathPage.hpp bindings\bindings\Geode\binding\GJPathRewardPopup.hpp bindings\bindings\Geode\binding\GJPathSprite.hpp bindings\bindings\Geode\binding\GJPathsLayer.hpp bindings\bindings\Geode\binding\GJPointDouble.hpp bindings\bindings\Geode\binding\GJPromoPopup.hpp bindings\bindings\Geode\binding\GJPurchaseDelegate.hpp bindings\bindings\Geode\binding\GJRequestCell.hpp bindings\bindings\Geode\binding\GJRewardDelegate.hpp bindings\bindings\Geode\binding\GJRewardItem.hpp bindings\bindings\Geode\binding\GJRewardObject.hpp bindings\bindings\Geode\binding\GJRobotSprite.hpp bindings\bindings\Geode\binding\GJRotateCommandLayer.hpp bindings\bindings\Geode\binding\GJRotationControl.hpp bindings\bindings\Geode\binding\GJRotationControlDelegate.hpp bindings\bindings\Geode\binding\GJScaleControl.hpp bindings\bindings\Geode\binding\GJScaleControlDelegate.hpp bindings\bindings\Geode\binding\GJScoreCell.hpp bindings\bindings\Geode\binding\GJSearchObject.hpp bindings\bindings\Geode\binding\GJShaderState.hpp bindings\bindings\Geode\binding\GJShopLayer.hpp bindings\bindings\Geode\binding\GJSmartBlockPreview.hpp bindings\bindings\Geode\binding\GJSmartBlockPreviewSprite.hpp bindings\bindings\Geode\binding\GJSmartPrefab.hpp bindings\bindings\Geode\binding\GJSmartTemplate.hpp bindings\bindings\Geode\binding\GJSongBrowser.hpp bindings\bindings\Geode\binding\GJSpecialColorSelect.hpp bindings\bindings\Geode\binding\GJSpecialColorSelectDelegate.hpp bindings\bindings\Geode\binding\GJSpiderSprite.hpp bindings\bindings\Geode\binding\GJSpriteColor.hpp bindings\bindings\Geode\binding\GJStoreItem.hpp bindings\bindings\Geode\binding\GJTransformControl.hpp bindings\bindings\Geode\binding\GJTransformControlDelegate.hpp bindings\bindings\Geode\binding\GJTransformState.hpp bindings\bindings\Geode\binding\GJUINode.hpp bindings\bindings\Geode\binding\GJUnlockableItem.hpp bindings\bindings\Geode\binding\GJUserCell.hpp bindings\bindings\Geode\binding\GJUserMessage.hpp bindings\bindings\Geode\binding\GJUserScore.hpp bindings\bindings\Geode\binding\GJValueTween.hpp bindings\bindings\Geode\binding\GJWorldNode.hpp bindings\bindings\Geode\binding\GJWriteMessagePopup.hpp bindings\bindings\Geode\binding\GManager.hpp bindings\bindings\Geode\binding\GameCell.hpp bindings\bindings\Geode\binding\GameEffectsManager.hpp bindings\bindings\Geode\binding\GameLevelManager.hpp bindings\bindings\Geode\binding\GameLevelOptionsLayer.hpp bindings\bindings\Geode\binding\GameManager.hpp bindings\bindings\Geode\binding\GameObject.hpp bindings\bindings\Geode\binding\GameObjectCopy.hpp bindings\bindings\Geode\binding\GameObjectEditorState.hpp bindings\bindings\Geode\binding\GameObjectPhysics.hpp bindings\bindings\Geode\binding\GameOptionsLayer.hpp bindings\bindings\Geode\binding\GameOptionsTrigger.hpp bindings\bindings\Geode\binding\GameRateDelegate.hpp bindings\bindings\Geode\binding\GameStatsManager.hpp bindings\bindings\Geode\binding\GameToolbox.hpp bindings\bindings\Geode\binding\GauntletLayer.hpp bindings\bindings\Geode\binding\GauntletNode.hpp bindings\bindings\Geode\binding\GauntletSelectLayer.hpp bindings\bindings\Geode\binding\GauntletSprite.hpp bindings\bindings\Geode\binding\GhostTrailEffect.hpp bindings\bindings\Geode\binding\GooglePlayDelegate.hpp bindings\bindings\Geode\binding\GooglePlayManager.hpp bindings\bindings\Geode\binding\GradientTriggerObject.hpp bindings\bindings\Geode\binding\GraphicsReloadLayer.hpp bindings\bindings\Geode\binding\GravityEffectSprite.hpp bindings\bindings\Geode\binding\GroupCommandObject2.hpp bindings\bindings\Geode\binding\HSVLiveOverlay.hpp bindings\bindings\Geode\binding\HSVWidgetDelegate.hpp bindings\bindings\Geode\binding\HSVWidgetPopup.hpp bindings\bindings\Geode\binding\HardStreak.hpp bindings\bindings\Geode\binding\InfoAlertButton.hpp bindings\bindings\Geode\binding\InfoLayer.hpp bindings\bindings\Geode\binding\InheritanceNode.hpp bindings\bindings\Geode\binding\ItemInfoPopup.hpp bindings\bindings\Geode\binding\ItemTriggerGameObject.hpp bindings\bindings\Geode\binding\KeybindingsLayer.hpp bindings\bindings\Geode\binding\KeybindingsManager.hpp bindings\bindings\Geode\binding\KeyframeAnimTriggerObject.hpp bindings\bindings\Geode\binding\KeyframeGameObject.hpp bindings\bindings\Geode\binding\KeyframeObject.hpp bindings\bindings\Geode\binding\LabelGameObject.hpp bindings\bindings\Geode\binding\LeaderboardManagerDelegate.hpp bindings\bindings\Geode\binding\LeaderboardsLayer.hpp bindings\bindings\Geode\binding\LevelAreaInnerLayer.hpp bindings\bindings\Geode\binding\LevelAreaLayer.hpp bindings\bindings\Geode\binding\LevelBrowserLayer.hpp bindings\bindings\Geode\binding\LevelCell.hpp bindings\bindings\Geode\binding\LevelCommentDelegate.hpp bindings\bindings\Geode\binding\LevelDeleteDelegate.hpp bindings\bindings\Geode\binding\LevelDownloadDelegate.hpp bindings\bindings\Geode\binding\LevelEditorLayer.hpp bindings\bindings\Geode\binding\LevelFeatureLayer.hpp bindings\bindings\Geode\binding\LevelInfoLayer.hpp bindings\bindings\Geode\binding\LevelLeaderboard.hpp bindings\bindings\Geode\binding\LevelListCell.hpp bindings\bindings\Geode\binding\LevelListDeleteDelegate.hpp bindings\bindings\Geode\binding\LevelListLayer.hpp bindings\bindings\Geode\binding\LevelManagerDelegate.hpp bindings\bindings\Geode\binding\LevelOptionsLayer.hpp bindings\bindings\Geode\binding\LevelOptionsLayer2.hpp bindings\bindings\Geode\binding\LevelPage.hpp bindings\bindings\Geode\binding\LevelSearchLayer.hpp bindings\bindings\Geode\binding\LevelSelectLayer.hpp bindings\bindings\Geode\binding\LevelSettingsDelegate.hpp bindings\bindings\Geode\binding\LevelSettingsLayer.hpp bindings\bindings\Geode\binding\LevelSettingsObject.hpp bindings\bindings\Geode\binding\LevelTools.hpp bindings\bindings\Geode\binding\LevelUpdateDelegate.hpp bindings\bindings\Geode\binding\LevelUploadDelegate.hpp bindings\bindings\Geode\binding\LikeItemDelegate.hpp bindings\bindings\Geode\binding\LikeItemLayer.hpp bindings\bindings\Geode\binding\ListButtonBar.hpp bindings\bindings\Geode\binding\ListButtonBarDelegate.hpp bindings\bindings\Geode\binding\ListButtonPage.hpp bindings\bindings\Geode\binding\ListCell.hpp bindings\bindings\Geode\binding\ListUploadDelegate.hpp bindings\bindings\Geode\binding\LoadingCircle.hpp bindings\bindings\Geode\binding\LoadingCircleSprite.hpp bindings\bindings\Geode\binding\LoadingLayer.hpp bindings\bindings\Geode\binding\LocalLevelManager.hpp bindings\bindings\Geode\binding\MPLobbyLayer.hpp bindings\bindings\Geode\binding\MapPackCell.hpp bindings\bindings\Geode\binding\MapSelectLayer.hpp bindings\bindings\Geode\binding\MenuGameLayer.hpp bindings\bindings\Geode\binding\MenuLayer.hpp bindings\bindings\Geode\binding\MessageListDelegate.hpp bindings\bindings\Geode\binding\MessagesProfilePage.hpp bindings\bindings\Geode\binding\MoreOptionsLayer.hpp bindings\bindings\Geode\binding\MoreSearchLayer.hpp bindings\bindings\Geode\binding\MoreVideoOptionsLayer.hpp bindings\bindings\Geode\binding\MultiTriggerPopup.hpp bindings\bindings\Geode\binding\MultilineBitmapFont.hpp bindings\bindings\Geode\binding\MultiplayerLayer.hpp bindings\bindings\Geode\binding\MusicArtistObject.hpp bindings\bindings\Geode\binding\MusicBrowser.hpp bindings\bindings\Geode\binding\MusicBrowserDelegate.hpp bindings\bindings\Geode\binding\MusicDelegateHandler.hpp bindings\bindings\Geode\binding\MusicDownloadDelegate.hpp bindings\bindings\Geode\binding\MusicDownloadManager.hpp bindings\bindings\Geode\binding\MusicSearchResult.hpp bindings\bindings\Geode\binding\NCSInfoLayer.hpp bindings\bindings\Geode\binding\NewgroundsInfoLayer.hpp bindings\bindings\Geode\binding\NodePoint.hpp bindings\bindings\Geode\binding\NumberInputDelegate.hpp bindings\bindings\Geode\binding\NumberInputLayer.hpp bindings\bindings\Geode\binding\OBB2D.hpp bindings\bindings\Geode\binding\ObjectControlGameObject.hpp bindings\bindings\Geode\binding\ObjectManager.hpp bindings\bindings\Geode\binding\ObjectToolbox.hpp bindings\bindings\Geode\binding\OnlineListDelegate.hpp bindings\bindings\Geode\binding\OpacityEffectAction.hpp bindings\bindings\Geode\binding\OptionsCell.hpp bindings\bindings\Geode\binding\OptionsLayer.hpp bindings\bindings\Geode\binding\OptionsObject.hpp bindings\bindings\Geode\binding\OptionsObjectDelegate.hpp bindings\bindings\Geode\binding\OptionsScrollLayer.hpp bindings\bindings\Geode\binding\ParentalOptionsLayer.hpp bindings\bindings\Geode\binding\ParticleGameObject.hpp bindings\bindings\Geode\binding\ParticlePreviewLayer.hpp bindings\bindings\Geode\binding\PauseLayer.hpp bindings\bindings\Geode\binding\PlatformDownloadDelegate.hpp bindings\bindings\Geode\binding\PlatformToolbox.hpp bindings\bindings\Geode\binding\PlayLayer.hpp bindings\bindings\Geode\binding\PlayerButtonCommand.hpp bindings\bindings\Geode\binding\PlayerCheckpoint.hpp bindings\bindings\Geode\binding\PlayerControlGameObject.hpp bindings\bindings\Geode\binding\PlayerFireBoostSprite.hpp bindings\bindings\Geode\binding\PlayerObject.hpp bindings\bindings\Geode\binding\PointNode.hpp bindings\bindings\Geode\binding\PriceLabel.hpp bindings\bindings\Geode\binding\ProfilePage.hpp bindings\bindings\Geode\binding\PromoInterstitial.hpp bindings\bindings\Geode\binding\PulseEffectAction.hpp bindings\bindings\Geode\binding\PurchaseItemPopup.hpp bindings\bindings\Geode\binding\RandTriggerGameObject.hpp bindings\bindings\Geode\binding\RateDemonLayer.hpp bindings\bindings\Geode\binding\RateLevelDelegate.hpp bindings\bindings\Geode\binding\RateLevelLayer.hpp bindings\bindings\Geode\binding\RateStarsLayer.hpp bindings\bindings\Geode\binding\RetryLevelLayer.hpp bindings\bindings\Geode\binding\RewardUnlockLayer.hpp bindings\bindings\Geode\binding\RewardedVideoDelegate.hpp bindings\bindings\Geode\binding\RewardsPage.hpp bindings\bindings\Geode\binding\RingObject.hpp bindings\bindings\Geode\binding\RotateGameplayGameObject.hpp bindings\bindings\Geode\binding\SFXBrowser.hpp bindings\bindings\Geode\binding\SFXBrowserDelegate.hpp bindings\bindings\Geode\binding\SFXFolderObject.hpp bindings\bindings\Geode\binding\SFXInfoObject.hpp bindings\bindings\Geode\binding\SFXSearchResult.hpp bindings\bindings\Geode\binding\SFXStateContainer.hpp bindings\bindings\Geode\binding\SFXTriggerGameObject.hpp bindings\bindings\Geode\binding\SFXTriggerInstance.hpp bindings\bindings\Geode\binding\SFXTriggerState.hpp bindings\bindings\Geode\binding\SavedActiveObjectState.hpp bindings\bindings\Geode\binding\SavedObjectStateRef.hpp bindings\bindings\Geode\binding\SavedSpecialObjectState.hpp bindings\bindings\Geode\binding\ScrollingLayer.hpp bindings\bindings\Geode\binding\SearchButton.hpp bindings\bindings\Geode\binding\SearchSFXPopup.hpp bindings\bindings\Geode\binding\SecretGame01Layer.hpp bindings\bindings\Geode\binding\SecretLayer.hpp bindings\bindings\Geode\binding\SecretLayer2.hpp bindings\bindings\Geode\binding\SecretLayer3.hpp bindings\bindings\Geode\binding\SecretLayer4.hpp bindings\bindings\Geode\binding\SecretLayer5.hpp bindings\bindings\Geode\binding\SecretLayer6.hpp bindings\bindings\Geode\binding\SecretNumberLayer.hpp bindings\bindings\Geode\binding\SecretRewardsLayer.hpp bindings\bindings\Geode\binding\SelectArtDelegate.hpp bindings\bindings\Geode\binding\SelectArtLayer.hpp bindings\bindings\Geode\binding\SelectEventLayer.hpp bindings\bindings\Geode\binding\SelectFontLayer.hpp bindings\bindings\Geode\binding\SelectListIconDelegate.hpp bindings\bindings\Geode\binding\SelectListIconLayer.hpp bindings\bindings\Geode\binding\SelectPremadeDelegate.hpp bindings\bindings\Geode\binding\SelectPremadeLayer.hpp bindings\bindings\Geode\binding\SelectSFXSortDelegate.hpp bindings\bindings\Geode\binding\SelectSFXSortLayer.hpp bindings\bindings\Geode\binding\SelectSettingDelegate.hpp bindings\bindings\Geode\binding\SelectSettingLayer.hpp bindings\bindings\Geode\binding\SequenceTriggerGameObject.hpp bindings\bindings\Geode\binding\SequenceTriggerState.hpp bindings\bindings\Geode\binding\SetColorIDPopup.hpp bindings\bindings\Geode\binding\SetFolderPopup.hpp bindings\bindings\Geode\binding\SetGroupIDLayer.hpp bindings\bindings\Geode\binding\SetIDPopup.hpp bindings\bindings\Geode\binding\SetIDPopupDelegate.hpp bindings\bindings\Geode\binding\SetItemIDLayer.hpp bindings\bindings\Geode\binding\SetLevelOrderPopup.hpp bindings\bindings\Geode\binding\SetTargetIDLayer.hpp bindings\bindings\Geode\binding\SetTextPopup.hpp bindings\bindings\Geode\binding\SetTextPopupDelegate.hpp bindings\bindings\Geode\binding\SetupAdvFollowEditPhysicsPopup.hpp bindings\bindings\Geode\binding\SetupAdvFollowPopup.hpp bindings\bindings\Geode\binding\SetupAdvFollowRetargetPopup.hpp bindings\bindings\Geode\binding\SetupAnimSettingsPopup.hpp bindings\bindings\Geode\binding\SetupAnimationPopup.hpp bindings\bindings\Geode\binding\SetupAreaAnimTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaFadeTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaMoveTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaRotateTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaTintTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaTransformTriggerPopup.hpp bindings\bindings\Geode\binding\SetupAreaTriggerPopup.hpp bindings\bindings\Geode\binding\SetupArtSwitchPopup.hpp bindings\bindings\Geode\binding\SetupAudioLineGuidePopup.hpp bindings\bindings\Geode\binding\SetupAudioTriggerPopup.hpp bindings\bindings\Geode\binding\SetupBGSpeedTrigger.hpp bindings\bindings\Geode\binding\SetupCameraEdgePopup.hpp bindings\bindings\Geode\binding\SetupCameraGuidePopup.hpp bindings\bindings\Geode\binding\SetupCameraModePopup.hpp bindings\bindings\Geode\binding\SetupCameraOffsetTrigger.hpp bindings\bindings\Geode\binding\SetupCameraRotatePopup.hpp bindings\bindings\Geode\binding\SetupCameraRotatePopup2.hpp bindings\bindings\Geode\binding\SetupCheckpointPopup.hpp bindings\bindings\Geode\binding\SetupCoinLayer.hpp bindings\bindings\Geode\binding\SetupCollisionStateTriggerPopup.hpp bindings\bindings\Geode\binding\SetupCollisionTriggerPopup.hpp bindings\bindings\Geode\binding\SetupCountTriggerPopup.hpp bindings\bindings\Geode\binding\SetupDashRingPopup.hpp bindings\bindings\Geode\binding\SetupEndPopup.hpp bindings\bindings\Geode\binding\SetupEnterEffectPopup.hpp bindings\bindings\Geode\binding\SetupEnterTriggerPopup.hpp bindings\bindings\Geode\binding\SetupEventLinkPopup.hpp bindings\bindings\Geode\binding\SetupForceBlockPopup.hpp bindings\bindings\Geode\binding\SetupGameplayOffsetPopup.hpp bindings\bindings\Geode\binding\SetupGradientPopup.hpp bindings\bindings\Geode\binding\SetupGravityModPopup.hpp bindings\bindings\Geode\binding\SetupGravityTriggerPopup.hpp bindings\bindings\Geode\binding\SetupInstantCollisionTriggerPopup.hpp bindings\bindings\Geode\binding\SetupInstantCountPopup.hpp bindings\bindings\Geode\binding\SetupInteractObjectPopup.hpp bindings\bindings\Geode\binding\SetupItemCompareTriggerPopup.hpp bindings\bindings\Geode\binding\SetupItemEditTriggerPopup.hpp bindings\bindings\Geode\binding\SetupKeyframeAnimPopup.hpp bindings\bindings\Geode\binding\SetupKeyframePopup.hpp bindings\bindings\Geode\binding\SetupMGTrigger.hpp bindings\bindings\Geode\binding\SetupMoveCommandPopup.hpp bindings\bindings\Geode\binding\SetupObjectControlPopup.hpp bindings\bindings\Geode\binding\SetupObjectOptions2Popup.hpp bindings\bindings\Geode\binding\SetupObjectOptionsPopup.hpp bindings\bindings\Geode\binding\SetupObjectTogglePopup.hpp bindings\bindings\Geode\binding\SetupOpacityPopup.hpp bindings\bindings\Geode\binding\SetupOptionsTriggerPopup.hpp bindings\bindings\Geode\binding\SetupPersistentItemTriggerPopup.hpp bindings\bindings\Geode\binding\SetupPickupTriggerPopup.hpp bindings\bindings\Geode\binding\SetupPlatformerEndPopup.hpp bindings\bindings\Geode\binding\SetupPlayerControlPopup.hpp bindings\bindings\Geode\binding\SetupPortalPopup.hpp bindings\bindings\Geode\binding\SetupPulsePopup.hpp bindings\bindings\Geode\binding\SetupRandAdvTriggerPopup.hpp bindings\bindings\Geode\binding\SetupRandTriggerPopup.hpp bindings\bindings\Geode\binding\SetupResetTriggerPopup.hpp bindings\bindings\Geode\binding\SetupReverbPopup.hpp bindings\bindings\Geode\binding\SetupRotateCommandPopup.hpp bindings\bindings\Geode\binding\SetupRotateGameplayPopup.hpp bindings\bindings\Geode\binding\SetupRotatePopup.hpp bindings\bindings\Geode\binding\SetupSFXEditPopup.hpp bindings\bindings\Geode\binding\SetupSFXPopup.hpp bindings\bindings\Geode\binding\SetupSequenceTriggerPopup.hpp bindings\bindings\Geode\binding\SetupShaderEffectPopup.hpp bindings\bindings\Geode\binding\SetupShakePopup.hpp bindings\bindings\Geode\binding\SetupSmartBlockLayer.hpp bindings\bindings\Geode\binding\SetupSmartTemplateLayer.hpp bindings\bindings\Geode\binding\SetupSongTriggerPopup.hpp bindings\bindings\Geode\binding\SetupSpawnParticlePopup.hpp bindings\bindings\Geode\binding\SetupSpawnPopup.hpp bindings\bindings\Geode\binding\SetupStaticCameraPopup.hpp bindings\bindings\Geode\binding\SetupStopTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTeleportPopup.hpp bindings\bindings\Geode\binding\SetupTimeWarpPopup.hpp bindings\bindings\Geode\binding\SetupTimerControlTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTimerEventTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTimerTriggerPopup.hpp bindings\bindings\Geode\binding\SetupTouchTogglePopup.hpp bindings\bindings\Geode\binding\SetupTransformPopup.hpp bindings\bindings\Geode\binding\SetupTriggerPopup.hpp bindings\bindings\Geode\binding\SetupZoomTriggerPopup.hpp bindings\bindings\Geode\binding\ShaderGameObject.hpp bindings\bindings\Geode\binding\ShaderLayer.hpp bindings\bindings\Geode\binding\ShardsPage.hpp bindings\bindings\Geode\binding\ShareCommentDelegate.hpp bindings\bindings\Geode\binding\ShareCommentLayer.hpp bindings\bindings\Geode\binding\ShareLevelLayer.hpp bindings\bindings\Geode\binding\ShareLevelSettingsLayer.hpp bindings\bindings\Geode\binding\ShareListLayer.hpp bindings\bindings\Geode\binding\SimpleObject.hpp bindings\bindings\Geode\binding\SimplePlayer.hpp bindings\bindings\Geode\binding\SlideInLayer.hpp bindings\bindings\Geode\binding\Slider.hpp bindings\bindings\Geode\binding\SliderDelegate.hpp bindings\bindings\Geode\binding\SliderThumb.hpp bindings\bindings\Geode\binding\SliderTouchLogic.hpp bindings\bindings\Geode\binding\SmartGameObject.hpp bindings\bindings\Geode\binding\SmartPrefabResult.hpp bindings\bindings\Geode\binding\SmartTemplateCell.hpp bindings\bindings\Geode\binding\SongCell.hpp bindings\bindings\Geode\binding\SongChannelState.hpp bindings\bindings\Geode\binding\SongInfoLayer.hpp bindings\bindings\Geode\binding\SongInfoObject.hpp bindings\bindings\Geode\binding\SongObject.hpp bindings\bindings\Geode\binding\SongOptionsLayer.hpp bindings\bindings\Geode\binding\SongPlaybackDelegate.hpp bindings\bindings\Geode\binding\SongSelectNode.hpp bindings\bindings\Geode\binding\SongTriggerGameObject.hpp bindings\bindings\Geode\binding\SongTriggerState.hpp bindings\bindings\Geode\binding\SongsLayer.hpp bindings\bindings\Geode\binding\SoundStateContainer.hpp bindings\bindings\Geode\binding\SpawnParticleGameObject.hpp bindings\bindings\Geode\binding\SpawnTriggerAction.hpp bindings\bindings\Geode\binding\SpawnTriggerGameObject.hpp bindings\bindings\Geode\binding\SpecialAnimGameObject.hpp bindings\bindings\Geode\binding\SpriteAnimationManager.hpp bindings\bindings\Geode\binding\SpriteDescription.hpp bindings\bindings\Geode\binding\SpritePartDelegate.hpp bindings\bindings\Geode\binding\Standalones.hpp bindings\bindings\Geode\binding\StarInfoPopup.hpp bindings\bindings\Geode\binding\StartPosObject.hpp bindings\bindings\Geode\binding\StatsCell.hpp bindings\bindings\Geode\binding\StatsLayer.hpp bindings\bindings\Geode\binding\StatsObject.hpp bindings\bindings\Geode\binding\SupportLayer.hpp bindings\bindings\Geode\binding\TOSPopup.hpp bindings\bindings\Geode\binding\TableView.hpp bindings\bindings\Geode\binding\TableViewCell.hpp bindings\bindings\Geode\binding\TableViewCellDelegate.hpp bindings\bindings\Geode\binding\TableViewDataSource.hpp bindings\bindings\Geode\binding\TableViewDelegate.hpp bindings\bindings\Geode\binding\TeleportPortalObject.hpp bindings\bindings\Geode\binding\TextAlertPopup.hpp bindings\bindings\Geode\binding\TextArea.hpp bindings\bindings\Geode\binding\TextAreaDelegate.hpp bindings\bindings\Geode\binding\TextGameObject.hpp bindings\bindings\Geode\binding\TextInputDelegate.hpp bindings\bindings\Geode\binding\TextStyleSection.hpp bindings\bindings\Geode\binding\TimerItem.hpp bindings\bindings\Geode\binding\TimerTriggerAction.hpp bindings\bindings\Geode\binding\TimerTriggerGameObject.hpp bindings\bindings\Geode\binding\ToggleTriggerAction.hpp bindings\bindings\Geode\binding\TopArtistsLayer.hpp bindings\bindings\Geode\binding\TouchToggleAction.hpp bindings\bindings\Geode\binding\TransformTriggerGameObject.hpp bindings\bindings\Geode\binding\TriggerControlGameObject.hpp bindings\bindings\Geode\binding\TriggerEffectDelegate.hpp bindings\bindings\Geode\binding\TutorialLayer.hpp bindings\bindings\Geode\binding\TutorialPopup.hpp bindings\bindings\Geode\binding\UIButtonConfig.hpp bindings\bindings\Geode\binding\UILayer.hpp bindings\bindings\Geode\binding\UIObjectSettingsPopup.hpp bindings\bindings\Geode\binding\UIOptionsLayer.hpp bindings\bindings\Geode\binding\UIPOptionsLayer.hpp bindings\bindings\Geode\binding\UISaveLoadLayer.hpp bindings\bindings\Geode\binding\UISettingsGameObject.hpp bindings\bindings\Geode\binding\URLCell.hpp bindings\bindings\Geode\binding\URLViewLayer.hpp bindings\bindings\Geode\binding\UndoObject.hpp bindings\bindings\Geode\binding\UpdateAccountSettingsPopup.hpp bindings\bindings\Geode\binding\UploadActionDelegate.hpp bindings\bindings\Geode\binding\UploadActionPopup.hpp bindings\bindings\Geode\binding\UploadListPopup.hpp bindings\bindings\Geode\binding\UploadMessageDelegate.hpp bindings\bindings\Geode\binding\UploadPopup.hpp bindings\bindings\Geode\binding\UploadPopupDelegate.hpp bindings\bindings\Geode\binding\UserInfoDelegate.hpp bindings\bindings\Geode\binding\UserListDelegate.hpp bindings\bindings\Geode\binding\VideoOptionsLayer.hpp bindings\bindings\Geode\binding\WorldLevelPage.hpp bindings\bindings\Geode\binding\WorldSelectLayer.hpp bindings\bindings\Geode\binding\tk_spline.hpp bindings\bindings\Geode\modify\AccountHelpLayer.hpp bindings\bindings\Geode\modify\AccountLayer.hpp bindings\bindings\Geode\modify\AccountLoginLayer.hpp bindings\bindings\Geode\modify\AccountRegisterLayer.hpp bindings\bindings\Geode\modify\AchievementBar.hpp bindings\bindings\Geode\modify\AchievementCell.hpp bindings\bindings\Geode\modify\AchievementManager.hpp bindings\bindings\Geode\modify\AchievementNotifier.hpp bindings\bindings\Geode\modify\AchievementsLayer.hpp bindings\bindings\Geode\modify\AdToolbox.hpp bindings\bindings\Geode\modify\AdvFollowSetup.hpp bindings\bindings\Geode\modify\AdvancedFollowEditObject.hpp bindings\bindings\Geode\modify\AdvancedFollowInstance.hpp bindings\bindings\Geode\modify\AdvancedFollowTriggerObject.hpp bindings\bindings\Geode\modify\AnimatedGameObject.hpp bindings\bindings\Geode\modify\AnimatedShopKeeper.hpp bindings\bindings\Geode\modify\AnimatedSpriteDelegate.hpp bindings\bindings\Geode\modify\AppDelegate.hpp bindings\bindings\Geode\modify\ArtTriggerGameObject.hpp bindings\bindings\Geode\modify\ArtistCell.hpp bindings\bindings\Geode\modify\AudioAssetsBrowser.hpp bindings\bindings\Geode\modify\AudioEffectsLayer.hpp bindings\bindings\Geode\modify\AudioLineGuideGameObject.hpp bindings\bindings\Geode\modify\BitmapFontCache.hpp bindings\bindings\Geode\modify\BonusDropdown.hpp bindings\bindings\Geode\modify\BoomListLayer.hpp bindings\bindings\Geode\modify\BoomListView.hpp bindings\bindings\Geode\modify\BoomScrollLayer.hpp bindings\bindings\Geode\modify\BoomScrollLayerDelegate.hpp bindings\bindings\Geode\modify\BrowseSmartKeyLayer.hpp bindings\bindings\Geode\modify\BrowseSmartTemplateLayer.hpp bindings\bindings\Geode\modify\ButtonPage.hpp bindings\bindings\Geode\modify\ButtonSprite.hpp bindings\bindings\Geode\modify\CAState.hpp bindings\bindings\Geode\modify\CCAction.hpp bindings\bindings\Geode\modify\CCActionCamera.hpp bindings\bindings\Geode\modify\CCActionEase.hpp bindings\bindings\Geode\modify\CCActionInstant.hpp bindings\bindings\Geode\modify\CCActionInterval.hpp bindings\bindings\Geode\modify\CCActionManager.hpp bindings\bindings\Geode\modify\CCAlertCircle.hpp bindings\bindings\Geode\modify\CCAnimate.hpp bindings\bindings\Geode\modify\CCAnimateFrameCache.hpp bindings\bindings\Geode\modify\CCAnimatedSprite.hpp bindings\bindings\Geode\modify\CCAnimation.hpp bindings\bindings\Geode\modify\CCAnimationCache.hpp bindings\bindings\Geode\modify\CCApplication.hpp bindings\bindings\Geode\modify\CCArray.hpp bindings\bindings\Geode\modify\CCBezierBy.hpp bindings\bindings\Geode\modify\CCBezierTo.hpp bindings\bindings\Geode\modify\CCBlink.hpp bindings\bindings\Geode\modify\CCBlockLayer.hpp bindings\bindings\Geode\modify\CCCallFunc.hpp bindings\bindings\Geode\modify\CCCallFuncN.hpp bindings\bindings\Geode\modify\CCCallFuncND.hpp bindings\bindings\Geode\modify\CCCallFuncO.hpp bindings\bindings\Geode\modify\CCCircleAlert.hpp bindings\bindings\Geode\modify\CCCircleWave.hpp bindings\bindings\Geode\modify\CCCircleWaveDelegate.hpp bindings\bindings\Geode\modify\CCClippingNode.hpp bindings\bindings\Geode\modify\CCConfiguration.hpp bindings\bindings\Geode\modify\CCContentLayer.hpp bindings\bindings\Geode\modify\CCContentManager.hpp bindings\bindings\Geode\modify\CCControl.hpp bindings\bindings\Geode\modify\CCControlColourPicker.hpp bindings\bindings\Geode\modify\CCControlHuePicker.hpp bindings\bindings\Geode\modify\CCControlSaturationBrightnessPicker.hpp bindings\bindings\Geode\modify\CCControlUtils.hpp bindings\bindings\Geode\modify\CCCountdown.hpp bindings\bindings\Geode\modify\CCCounterLabel.hpp bindings\bindings\Geode\modify\CCDelayTime.hpp bindings\bindings\Geode\modify\CCDictionary.hpp bindings\bindings\Geode\modify\CCDirector.hpp bindings\bindings\Geode\modify\CCDisplayLinkDirector.hpp bindings\bindings\Geode\modify\CCDrawNode.hpp bindings\bindings\Geode\modify\CCEGLView.hpp bindings\bindings\Geode\modify\CCEGLViewProtocol.hpp bindings\bindings\Geode\modify\CCEaseBackIn.hpp bindings\bindings\Geode\modify\CCEaseBackInOut.hpp bindings\bindings\Geode\modify\CCEaseBackOut.hpp bindings\bindings\Geode\modify\CCEaseBounce.hpp bindings\bindings\Geode\modify\CCEaseBounceIn.hpp bindings\bindings\Geode\modify\CCEaseBounceInOut.hpp bindings\bindings\Geode\modify\CCEaseBounceOut.hpp bindings\bindings\Geode\modify\CCEaseElastic.hpp bindings\bindings\Geode\modify\CCEaseElasticIn.hpp bindings\bindings\Geode\modify\CCEaseElasticInOut.hpp bindings\bindings\Geode\modify\CCEaseElasticOut.hpp bindings\bindings\Geode\modify\CCEaseExponentialIn.hpp bindings\bindings\Geode\modify\CCEaseExponentialInOut.hpp bindings\bindings\Geode\modify\CCEaseExponentialOut.hpp bindings\bindings\Geode\modify\CCEaseIn.hpp bindings\bindings\Geode\modify\CCEaseInOut.hpp bindings\bindings\Geode\modify\CCEaseOut.hpp bindings\bindings\Geode\modify\CCEaseRateAction.hpp bindings\bindings\Geode\modify\CCEaseSineIn.hpp bindings\bindings\Geode\modify\CCEaseSineInOut.hpp bindings\bindings\Geode\modify\CCEaseSineOut.hpp bindings\bindings\Geode\modify\CCExtenderNode.hpp bindings\bindings\Geode\modify\CCFadeIn.hpp bindings\bindings\Geode\modify\CCFadeOut.hpp bindings\bindings\Geode\modify\CCFadeTo.hpp bindings\bindings\Geode\modify\CCFileUtils.hpp bindings\bindings\Geode\modify\CCFiniteTimeAction.hpp bindings\bindings\Geode\modify\CCGLProgram.hpp bindings\bindings\Geode\modify\CCHide.hpp bindings\bindings\Geode\modify\CCHttpClient.hpp bindings\bindings\Geode\modify\CCHttpRequest.hpp bindings\bindings\Geode\modify\CCHttpResponse.hpp bindings\bindings\Geode\modify\CCIMEDelegate.hpp bindings\bindings\Geode\modify\CCIMEDispatcher.hpp bindings\bindings\Geode\modify\CCImage.hpp bindings\bindings\Geode\modify\CCIndexPath.hpp bindings\bindings\Geode\modify\CCJumpBy.hpp bindings\bindings\Geode\modify\CCJumpTo.hpp bindings\bindings\Geode\modify\CCKeyboardDispatcher.hpp bindings\bindings\Geode\modify\CCKeypadDispatcher.hpp bindings\bindings\Geode\modify\CCLabelBMFont.hpp bindings\bindings\Geode\modify\CCLabelTTF.hpp bindings\bindings\Geode\modify\CCLayer.hpp bindings\bindings\Geode\modify\CCLayerColor.hpp bindings\bindings\Geode\modify\CCLayerGradient.hpp bindings\bindings\Geode\modify\CCLayerRGBA.hpp bindings\bindings\Geode\modify\CCLightFlash.hpp bindings\bindings\Geode\modify\CCLightStrip.hpp bindings\bindings\Geode\modify\CCLightning.hpp bindings\bindings\Geode\modify\CCMenu.hpp bindings\bindings\Geode\modify\CCMenuItem.hpp bindings\bindings\Geode\modify\CCMenuItemImage.hpp bindings\bindings\Geode\modify\CCMenuItemSprite.hpp bindings\bindings\Geode\modify\CCMenuItemSpriteExtra.hpp bindings\bindings\Geode\modify\CCMenuItemToggler.hpp bindings\bindings\Geode\modify\CCMotionStreak.hpp bindings\bindings\Geode\modify\CCMouseDispatcher.hpp bindings\bindings\Geode\modify\CCMouseHandler.hpp bindings\bindings\Geode\modify\CCMoveBy.hpp bindings\bindings\Geode\modify\CCMoveCNode.hpp bindings\bindings\Geode\modify\CCMoveTo.hpp bindings\bindings\Geode\modify\CCNode.hpp bindings\bindings\Geode\modify\CCNodeContainer.hpp bindings\bindings\Geode\modify\CCNodeRGBA.hpp bindings\bindings\Geode\modify\CCObject.hpp bindings\bindings\Geode\modify\CCOrbitCamera.hpp bindings\bindings\Geode\modify\CCPartAnimSprite.hpp bindings\bindings\Geode\modify\CCParticleExplosion.hpp bindings\bindings\Geode\modify\CCParticleFire.hpp bindings\bindings\Geode\modify\CCParticleRain.hpp bindings\bindings\Geode\modify\CCParticleSnow.hpp bindings\bindings\Geode\modify\CCParticleSystem.hpp bindings\bindings\Geode\modify\CCParticleSystemQuad.hpp bindings\bindings\Geode\modify\CCPoolManager.hpp bindings\bindings\Geode\modify\CCProgressTimer.hpp bindings\bindings\Geode\modify\CCRemoveSelf.hpp bindings\bindings\Geode\modify\CCRenderTexture.hpp bindings\bindings\Geode\modify\CCRepeat.hpp bindings\bindings\Geode\modify\CCRepeatForever.hpp bindings\bindings\Geode\modify\CCRotateBy.hpp bindings\bindings\Geode\modify\CCRotateTo.hpp bindings\bindings\Geode\modify\CCScale9Sprite.hpp bindings\bindings\Geode\modify\CCScaleBy.hpp bindings\bindings\Geode\modify\CCScaleTo.hpp bindings\bindings\Geode\modify\CCScene.hpp bindings\bindings\Geode\modify\CCScheduler.hpp bindings\bindings\Geode\modify\CCScriptEngineManager.hpp bindings\bindings\Geode\modify\CCScrollLayerExt.hpp bindings\bindings\Geode\modify\CCScrollLayerExtDelegate.hpp bindings\bindings\Geode\modify\CCSequence.hpp bindings\bindings\Geode\modify\CCSet.hpp bindings\bindings\Geode\modify\CCShaderCache.hpp bindings\bindings\Geode\modify\CCShow.hpp bindings\bindings\Geode\modify\CCSkewBy.hpp bindings\bindings\Geode\modify\CCSkewTo.hpp bindings\bindings\Geode\modify\CCSpawn.hpp bindings\bindings\Geode\modify\CCSpeed.hpp bindings\bindings\Geode\modify\CCSprite.hpp bindings\bindings\Geode\modify\CCSpriteBatchNode.hpp bindings\bindings\Geode\modify\CCSpriteCOpacity.hpp bindings\bindings\Geode\modify\CCSpriteFrame.hpp bindings\bindings\Geode\modify\CCSpriteFrameCache.hpp bindings\bindings\Geode\modify\CCSpriteGrayscale.hpp bindings\bindings\Geode\modify\CCSpritePart.hpp bindings\bindings\Geode\modify\CCSpritePlus.hpp bindings\bindings\Geode\modify\CCSpriteWithHue.hpp bindings\bindings\Geode\modify\CCString.hpp bindings\bindings\Geode\modify\CCTargetedTouchHandler.hpp bindings\bindings\Geode\modify\CCTextFieldTTF.hpp bindings\bindings\Geode\modify\CCTextInputNode.hpp bindings\bindings\Geode\modify\CCTexture2D.hpp bindings\bindings\Geode\modify\CCTextureAtlas.hpp bindings\bindings\Geode\modify\CCTextureCache.hpp bindings\bindings\Geode\modify\CCTintTo.hpp bindings\bindings\Geode\modify\CCTouch.hpp bindings\bindings\Geode\modify\CCTouchDelegate.hpp bindings\bindings\Geode\modify\CCTouchDispatcher.hpp bindings\bindings\Geode\modify\CCTouchHandler.hpp bindings\bindings\Geode\modify\CCTransitionCrossFade.hpp bindings\bindings\Geode\modify\CCTransitionFade.hpp bindings\bindings\Geode\modify\CCTransitionFadeBL.hpp bindings\bindings\Geode\modify\CCTransitionFadeDown.hpp bindings\bindings\Geode\modify\CCTransitionFadeTR.hpp bindings\bindings\Geode\modify\CCTransitionFadeUp.hpp bindings\bindings\Geode\modify\CCTransitionFlipAngular.hpp bindings\bindings\Geode\modify\CCTransitionFlipX.hpp bindings\bindings\Geode\modify\CCTransitionFlipY.hpp bindings\bindings\Geode\modify\CCTransitionJumpZoom.hpp bindings\bindings\Geode\modify\CCTransitionMoveInB.hpp bindings\bindings\Geode\modify\CCTransitionMoveInL.hpp bindings\bindings\Geode\modify\CCTransitionMoveInR.hpp bindings\bindings\Geode\modify\CCTransitionMoveInT.hpp bindings\bindings\Geode\modify\CCTransitionRotoZoom.hpp bindings\bindings\Geode\modify\CCTransitionScene.hpp bindings\bindings\Geode\modify\CCTransitionShrinkGrow.hpp bindings\bindings\Geode\modify\CCTransitionSlideInB.hpp bindings\bindings\Geode\modify\CCTransitionSlideInL.hpp bindings\bindings\Geode\modify\CCTransitionSlideInR.hpp bindings\bindings\Geode\modify\CCTransitionSlideInT.hpp bindings\bindings\Geode\modify\CCTransitionSplitCols.hpp bindings\bindings\Geode\modify\CCTransitionSplitRows.hpp bindings\bindings\Geode\modify\CCTransitionTurnOffTiles.hpp bindings\bindings\Geode\modify\CCTransitionZoomFlipAngular.hpp bindings\bindings\Geode\modify\CCTransitionZoomFlipX.hpp bindings\bindings\Geode\modify\CCTransitionZoomFlipY.hpp bindings\bindings\Geode\modify\CCTurnOffTiles.hpp bindings\bindings\Geode\modify\CCURLObject.hpp bindings\bindings\Geode\modify\CCZone.hpp bindings\bindings\Geode\modify\CameraTriggerGameObject.hpp bindings\bindings\Geode\modify\ChallengeNode.hpp bindings\bindings\Geode\modify\ChallengesPage.hpp bindings\bindings\Geode\modify\ChanceObject.hpp bindings\bindings\Geode\modify\ChanceTriggerGameObject.hpp bindings\bindings\Geode\modify\Channel.hpp bindings\bindings\Geode\modify\ChannelControl.hpp bindings\bindings\Geode\modify\ChannelGroup.hpp bindings\bindings\Geode\modify\CharacterColorDelegate.hpp bindings\bindings\Geode\modify\CharacterColorPage.hpp bindings\bindings\Geode\modify\CheckpointGameObject.hpp bindings\bindings\Geode\modify\CheckpointObject.hpp bindings\bindings\Geode\modify\CollisionBlockPopup.hpp bindings\bindings\Geode\modify\CollisionTriggerAction.hpp bindings\bindings\Geode\modify\ColorAction.hpp bindings\bindings\Geode\modify\ColorAction2.hpp bindings\bindings\Geode\modify\ColorActionSprite.hpp bindings\bindings\Geode\modify\ColorChannelSprite.hpp bindings\bindings\Geode\modify\ColorSelectDelegate.hpp bindings\bindings\Geode\modify\ColorSelectLiveOverlay.hpp bindings\bindings\Geode\modify\ColorSelectPopup.hpp bindings\bindings\Geode\modify\ColorSetupDelegate.hpp bindings\bindings\Geode\modify\CommentCell.hpp bindings\bindings\Geode\modify\CommentUploadDelegate.hpp bindings\bindings\Geode\modify\CommunityCreditNode.hpp bindings\bindings\Geode\modify\CommunityCreditsPage.hpp bindings\bindings\Geode\modify\ConfigureHSVWidget.hpp bindings\bindings\Geode\modify\ConfigureValuePopup.hpp bindings\bindings\Geode\modify\ConfigureValuePopupDelegate.hpp bindings\bindings\Geode\modify\CountTriggerAction.hpp bindings\bindings\Geode\modify\CountTriggerGameObject.hpp bindings\bindings\Geode\modify\CreateGuidelinesLayer.hpp bindings\bindings\Geode\modify\CreateMenuItem.hpp bindings\bindings\Geode\modify\CreateParticlePopup.hpp bindings\bindings\Geode\modify\CreatorLayer.hpp bindings\bindings\Geode\modify\CurrencyRewardDelegate.hpp bindings\bindings\Geode\modify\CurrencyRewardLayer.hpp bindings\bindings\Geode\modify\CurrencySprite.hpp bindings\bindings\Geode\modify\CustomListView.hpp bindings\bindings\Geode\modify\CustomMusicCell.hpp bindings\bindings\Geode\modify\CustomSFXCell.hpp bindings\bindings\Geode\modify\CustomSFXDelegate.hpp bindings\bindings\Geode\modify\CustomSFXWidget.hpp bindings\bindings\Geode\modify\CustomSongCell.hpp bindings\bindings\Geode\modify\CustomSongDelegate.hpp bindings\bindings\Geode\modify\CustomSongLayer.hpp bindings\bindings\Geode\modify\CustomSongLayerDelegate.hpp bindings\bindings\Geode\modify\CustomSongWidget.hpp bindings\bindings\Geode\modify\CustomizeObjectLayer.hpp bindings\bindings\Geode\modify\CustomizeObjectSettingsPopup.hpp bindings\bindings\Geode\modify\DSP.hpp bindings\bindings\Geode\modify\DSPConnection.hpp bindings\bindings\Geode\modify\DS_Dictionary.hpp bindings\bindings\Geode\modify\DailyLevelNode.hpp bindings\bindings\Geode\modify\DailyLevelPage.hpp bindings\bindings\Geode\modify\DashRingObject.hpp bindings\bindings\Geode\modify\DelayedSpawnNode.hpp bindings\bindings\Geode\modify\DemonFilterDelegate.hpp bindings\bindings\Geode\modify\DemonFilterSelectLayer.hpp bindings\bindings\Geode\modify\DemonInfoPopup.hpp bindings\bindings\Geode\modify\DialogDelegate.hpp bindings\bindings\Geode\modify\DialogLayer.hpp bindings\bindings\Geode\modify\DialogObject.hpp bindings\bindings\Geode\modify\DownloadMessageDelegate.hpp bindings\bindings\Geode\modify\DrawGridLayer.hpp bindings\bindings\Geode\modify\DungeonBarsSprite.hpp bindings\bindings\Geode\modify\DynamicBitset.hpp bindings\bindings\Geode\modify\DynamicMoveCalculation.hpp bindings\bindings\Geode\modify\DynamicObjectAction.hpp bindings\bindings\Geode\modify\DynamicScrollDelegate.hpp bindings\bindings\Geode\modify\EditButtonBar.hpp bindings\bindings\Geode\modify\EditGameObjectPopup.hpp bindings\bindings\Geode\modify\EditLevelLayer.hpp bindings\bindings\Geode\modify\EditTriggersPopup.hpp bindings\bindings\Geode\modify\EditorOptionsLayer.hpp bindings\bindings\Geode\modify\EditorPauseLayer.hpp bindings\bindings\Geode\modify\EditorUI.hpp bindings\bindings\Geode\modify\EffectGameObject.hpp bindings\bindings\Geode\modify\EffectManagerState.hpp bindings\bindings\Geode\modify\EndLevelLayer.hpp bindings\bindings\Geode\modify\EndPortalObject.hpp bindings\bindings\Geode\modify\EndTriggerGameObject.hpp bindings\bindings\Geode\modify\EnhancedGameObject.hpp bindings\bindings\Geode\modify\EnhancedTriggerObject.hpp bindings\bindings\Geode\modify\EnterEffectAnimValue.hpp bindings\bindings\Geode\modify\EnterEffectInstance.hpp bindings\bindings\Geode\modify\EnterEffectObject.hpp bindings\bindings\Geode\modify\EventLinkTrigger.hpp bindings\bindings\Geode\modify\EventTriggerInstance.hpp bindings\bindings\Geode\modify\ExplodeItemNode.hpp bindings\bindings\Geode\modify\ExplodeItemSprite.hpp bindings\bindings\Geode\modify\ExtendedLayer.hpp bindings\bindings\Geode\modify\FLAlertLayer.hpp bindings\bindings\Geode\modify\FLAlertLayerProtocol.hpp bindings\bindings\Geode\modify\FMODAudioEngine.hpp bindings\bindings\Geode\modify\FMODAudioState.hpp bindings\bindings\Geode\modify\FMODLevelVisualizer.hpp bindings\bindings\Geode\modify\FMODMusic.hpp bindings\bindings\Geode\modify\FMODQueuedEffect.hpp bindings\bindings\Geode\modify\FMODQueuedMusic.hpp bindings\bindings\Geode\modify\FMODSound.hpp bindings\bindings\Geode\modify\FMODSoundState.hpp bindings\bindings\Geode\modify\FMODSoundTween.hpp bindings\bindings\Geode\modify\FRequestProfilePage.hpp bindings\bindings\Geode\modify\FileOperation.hpp bindings\bindings\Geode\modify\FileSaveManager.hpp bindings\bindings\Geode\modify\FindBPMLayer.hpp bindings\bindings\Geode\modify\FindObjectPopup.hpp bindings\bindings\Geode\modify\FollowRewardPage.hpp bindings\bindings\Geode\modify\FontObject.hpp bindings\bindings\Geode\modify\ForceBlockGameObject.hpp bindings\bindings\Geode\modify\FriendRequestDelegate.hpp bindings\bindings\Geode\modify\FriendRequestPopup.hpp bindings\bindings\Geode\modify\FriendsProfilePage.hpp bindings\bindings\Geode\modify\GJAccountBackupDelegate.hpp bindings\bindings\Geode\modify\GJAccountDelegate.hpp bindings\bindings\Geode\modify\GJAccountLoginDelegate.hpp bindings\bindings\Geode\modify\GJAccountManager.hpp bindings\bindings\Geode\modify\GJAccountRegisterDelegate.hpp bindings\bindings\Geode\modify\GJAccountSettingsDelegate.hpp bindings\bindings\Geode\modify\GJAccountSettingsLayer.hpp bindings\bindings\Geode\modify\GJAccountSyncDelegate.hpp bindings\bindings\Geode\modify\GJActionManager.hpp bindings\bindings\Geode\modify\GJAssetDownloadAction.hpp bindings\bindings\Geode\modify\GJBaseGameLayer.hpp bindings\bindings\Geode\modify\GJBigSprite.hpp bindings\bindings\Geode\modify\GJBigSpriteNode.hpp bindings\bindings\Geode\modify\GJChallengeDelegate.hpp bindings\bindings\Geode\modify\GJChallengeItem.hpp bindings\bindings\Geode\modify\GJChestSprite.hpp bindings\bindings\Geode\modify\GJColorSetupLayer.hpp bindings\bindings\Geode\modify\GJComment.hpp bindings\bindings\Geode\modify\GJCommentListLayer.hpp bindings\bindings\Geode\modify\GJDailyLevelDelegate.hpp bindings\bindings\Geode\modify\GJDifficultySprite.hpp bindings\bindings\Geode\modify\GJDropDownLayer.hpp bindings\bindings\Geode\modify\GJDropDownLayerDelegate.hpp bindings\bindings\Geode\modify\GJEffectManager.hpp bindings\bindings\Geode\modify\GJFlyGroundLayer.hpp bindings\bindings\Geode\modify\GJFollowCommandLayer.hpp bindings\bindings\Geode\modify\GJFriendRequest.hpp bindings\bindings\Geode\modify\GJGameLevel.hpp bindings\bindings\Geode\modify\GJGameLoadingLayer.hpp bindings\bindings\Geode\modify\GJGameState.hpp bindings\bindings\Geode\modify\GJGarageLayer.hpp bindings\bindings\Geode\modify\GJGradientLayer.hpp bindings\bindings\Geode\modify\GJGroundLayer.hpp bindings\bindings\Geode\modify\GJHttpResult.hpp bindings\bindings\Geode\modify\GJItemIcon.hpp bindings\bindings\Geode\modify\GJLevelList.hpp bindings\bindings\Geode\modify\GJLevelScoreCell.hpp bindings\bindings\Geode\modify\GJListLayer.hpp bindings\bindings\Geode\modify\GJLocalLevelScoreCell.hpp bindings\bindings\Geode\modify\GJLocalScore.hpp bindings\bindings\Geode\modify\GJMGLayer.hpp bindings\bindings\Geode\modify\GJMPDelegate.hpp bindings\bindings\Geode\modify\GJMapObject.hpp bindings\bindings\Geode\modify\GJMapPack.hpp bindings\bindings\Geode\modify\GJMessageCell.hpp bindings\bindings\Geode\modify\GJMessagePopup.hpp bindings\bindings\Geode\modify\GJMoreGamesLayer.hpp bindings\bindings\Geode\modify\GJMultiplayerManager.hpp bindings\bindings\Geode\modify\GJObjectDecoder.hpp bindings\bindings\Geode\modify\GJOnlineRewardDelegate.hpp bindings\bindings\Geode\modify\GJOptionsLayer.hpp bindings\bindings\Geode\modify\GJPFollowCommandLayer.hpp bindings\bindings\Geode\modify\GJPathPage.hpp bindings\bindings\Geode\modify\GJPathRewardPopup.hpp bindings\bindings\Geode\modify\GJPathSprite.hpp bindings\bindings\Geode\modify\GJPathsLayer.hpp bindings\bindings\Geode\modify\GJPointDouble.hpp bindings\bindings\Geode\modify\GJPromoPopup.hpp bindings\bindings\Geode\modify\GJPurchaseDelegate.hpp bindings\bindings\Geode\modify\GJRequestCell.hpp bindings\bindings\Geode\modify\GJRewardDelegate.hpp bindings\bindings\Geode\modify\GJRewardItem.hpp bindings\bindings\Geode\modify\GJRewardObject.hpp bindings\bindings\Geode\modify\GJRobotSprite.hpp bindings\bindings\Geode\modify\GJRotateCommandLayer.hpp bindings\bindings\Geode\modify\GJRotationControl.hpp bindings\bindings\Geode\modify\GJRotationControlDelegate.hpp bindings\bindings\Geode\modify\GJScaleControl.hpp bindings\bindings\Geode\modify\GJScaleControlDelegate.hpp bindings\bindings\Geode\modify\GJScoreCell.hpp bindings\bindings\Geode\modify\GJSearchObject.hpp bindings\bindings\Geode\modify\GJShaderState.hpp bindings\bindings\Geode\modify\GJShopLayer.hpp bindings\bindings\Geode\modify\GJSmartBlockPreview.hpp bindings\bindings\Geode\modify\GJSmartBlockPreviewSprite.hpp bindings\bindings\Geode\modify\GJSmartPrefab.hpp bindings\bindings\Geode\modify\GJSmartTemplate.hpp bindings\bindings\Geode\modify\GJSongBrowser.hpp bindings\bindings\Geode\modify\GJSpecialColorSelect.hpp bindings\bindings\Geode\modify\GJSpecialColorSelectDelegate.hpp bindings\bindings\Geode\modify\GJSpiderSprite.hpp bindings\bindings\Geode\modify\GJSpriteColor.hpp bindings\bindings\Geode\modify\GJStoreItem.hpp bindings\bindings\Geode\modify\GJTransformControl.hpp bindings\bindings\Geode\modify\GJTransformControlDelegate.hpp bindings\bindings\Geode\modify\GJTransformState.hpp bindings\bindings\Geode\modify\GJUINode.hpp bindings\bindings\Geode\modify\GJUnlockableItem.hpp bindings\bindings\Geode\modify\GJUserCell.hpp bindings\bindings\Geode\modify\GJUserMessage.hpp bindings\bindings\Geode\modify\GJUserScore.hpp bindings\bindings\Geode\modify\GJValueTween.hpp bindings\bindings\Geode\modify\GJWorldNode.hpp bindings\bindings\Geode\modify\GJWriteMessagePopup.hpp bindings\bindings\Geode\modify\GManager.hpp bindings\bindings\Geode\modify\GameCell.hpp bindings\bindings\Geode\modify\GameEffectsManager.hpp bindings\bindings\Geode\modify\GameLevelManager.hpp bindings\bindings\Geode\modify\GameLevelOptionsLayer.hpp bindings\bindings\Geode\modify\GameManager.hpp bindings\bindings\Geode\modify\GameObject.hpp bindings\bindings\Geode\modify\GameObjectCopy.hpp bindings\bindings\Geode\modify\GameObjectEditorState.hpp bindings\bindings\Geode\modify\GameObjectPhysics.hpp bindings\bindings\Geode\modify\GameOptionsLayer.hpp bindings\bindings\Geode\modify\GameOptionsTrigger.hpp bindings\bindings\Geode\modify\GameRateDelegate.hpp bindings\bindings\Geode\modify\GameStatsManager.hpp bindings\bindings\Geode\modify\GameToolbox.hpp bindings\bindings\Geode\modify\GauntletLayer.hpp bindings\bindings\Geode\modify\GauntletNode.hpp bindings\bindings\Geode\modify\GauntletSelectLayer.hpp bindings\bindings\Geode\modify\GauntletSprite.hpp bindings\bindings\Geode\modify\Geometry.hpp bindings\bindings\Geode\modify\GhostTrailEffect.hpp bindings\bindings\Geode\modify\GooglePlayDelegate.hpp bindings\bindings\Geode\modify\GooglePlayManager.hpp bindings\bindings\Geode\modify\GradientTriggerObject.hpp bindings\bindings\Geode\modify\GraphicsReloadLayer.hpp bindings\bindings\Geode\modify\GravityEffectSprite.hpp bindings\bindings\Geode\modify\GroupCommandObject2.hpp bindings\bindings\Geode\modify\HSVLiveOverlay.hpp bindings\bindings\Geode\modify\HSVWidgetDelegate.hpp bindings\bindings\Geode\modify\HSVWidgetPopup.hpp bindings\bindings\Geode\modify\HardStreak.hpp bindings\bindings\Geode\modify\InfoAlertButton.hpp bindings\bindings\Geode\modify\InfoLayer.hpp bindings\bindings\Geode\modify\InheritanceNode.hpp bindings\bindings\Geode\modify\ItemInfoPopup.hpp bindings\bindings\Geode\modify\ItemTriggerGameObject.hpp bindings\bindings\Geode\modify\KeybindingsLayer.hpp bindings\bindings\Geode\modify\KeybindingsManager.hpp bindings\bindings\Geode\modify\KeyframeAnimTriggerObject.hpp bindings\bindings\Geode\modify\KeyframeGameObject.hpp bindings\bindings\Geode\modify\KeyframeObject.hpp bindings\bindings\Geode\modify\LabelGameObject.hpp bindings\bindings\Geode\modify\LeaderboardManagerDelegate.hpp bindings\bindings\Geode\modify\LeaderboardsLayer.hpp bindings\bindings\Geode\modify\LevelAreaInnerLayer.hpp bindings\bindings\Geode\modify\LevelAreaLayer.hpp bindings\bindings\Geode\modify\LevelBrowserLayer.hpp bindings\bindings\Geode\modify\LevelCell.hpp bindings\bindings\Geode\modify\LevelCommentDelegate.hpp bindings\bindings\Geode\modify\LevelDeleteDelegate.hpp bindings\bindings\Geode\modify\LevelDownloadDelegate.hpp bindings\bindings\Geode\modify\LevelEditorLayer.hpp bindings\bindings\Geode\modify\LevelFeatureLayer.hpp bindings\bindings\Geode\modify\LevelInfoLayer.hpp bindings\bindings\Geode\modify\LevelLeaderboard.hpp bindings\bindings\Geode\modify\LevelListCell.hpp bindings\bindings\Geode\modify\LevelListDeleteDelegate.hpp bindings\bindings\Geode\modify\LevelListLayer.hpp bindings\bindings\Geode\modify\LevelManagerDelegate.hpp bindings\bindings\Geode\modify\LevelOptionsLayer.hpp bindings\bindings\Geode\modify\LevelOptionsLayer2.hpp bindings\bindings\Geode\modify\LevelPage.hpp bindings\bindings\Geode\modify\LevelSearchLayer.hpp bindings\bindings\Geode\modify\LevelSelectLayer.hpp bindings\bindings\Geode\modify\LevelSettingsDelegate.hpp bindings\bindings\Geode\modify\LevelSettingsLayer.hpp bindings\bindings\Geode\modify\LevelSettingsObject.hpp bindings\bindings\Geode\modify\LevelTools.hpp bindings\bindings\Geode\modify\LevelUpdateDelegate.hpp bindings\bindings\Geode\modify\LevelUploadDelegate.hpp bindings\bindings\Geode\modify\LikeItemDelegate.hpp bindings\bindings\Geode\modify\LikeItemLayer.hpp bindings\bindings\Geode\modify\ListButtonBar.hpp bindings\bindings\Geode\modify\ListButtonBarDelegate.hpp bindings\bindings\Geode\modify\ListButtonPage.hpp bindings\bindings\Geode\modify\ListCell.hpp bindings\bindings\Geode\modify\ListUploadDelegate.hpp bindings\bindings\Geode\modify\LoadingCircle.hpp bindings\bindings\Geode\modify\LoadingCircleSprite.hpp bindings\bindings\Geode\modify\LoadingLayer.hpp bindings\bindings\Geode\modify\LocalLevelManager.hpp bindings\bindings\Geode\modify\MPLobbyLayer.hpp bindings\bindings\Geode\modify\MapPackCell.hpp bindings\bindings\Geode\modify\MapSelectLayer.hpp bindings\bindings\Geode\modify\MenuGameLayer.hpp bindings\bindings\Geode\modify\MenuLayer.hpp bindings\bindings\Geode\modify\MessageListDelegate.hpp bindings\bindings\Geode\modify\MessagesProfilePage.hpp bindings\bindings\Geode\modify\MoreOptionsLayer.hpp bindings\bindings\Geode\modify\MoreSearchLayer.hpp bindings\bindings\Geode\modify\MoreVideoOptionsLayer.hpp bindings\bindings\Geode\modify\MultiTriggerPopup.hpp bindings\bindings\Geode\modify\MultilineBitmapFont.hpp bindings\bindings\Geode\modify\MultiplayerLayer.hpp bindings\bindings\Geode\modify\MusicArtistObject.hpp bindings\bindings\Geode\modify\MusicBrowser.hpp bindings\bindings\Geode\modify\MusicBrowserDelegate.hpp bindings\bindings\Geode\modify\MusicDelegateHandler.hpp bindings\bindings\Geode\modify\MusicDownloadDelegate.hpp bindings\bindings\Geode\modify\MusicDownloadManager.hpp bindings\bindings\Geode\modify\MusicSearchResult.hpp bindings\bindings\Geode\modify\NCSInfoLayer.hpp bindings\bindings\Geode\modify\NewgroundsInfoLayer.hpp bindings\bindings\Geode\modify\NodePoint.hpp bindings\bindings\Geode\modify\NumberInputDelegate.hpp bindings\bindings\Geode\modify\NumberInputLayer.hpp bindings\bindings\Geode\modify\OBB2D.hpp bindings\bindings\Geode\modify\ObjectControlGameObject.hpp bindings\bindings\Geode\modify\ObjectDecoder.hpp bindings\bindings\Geode\modify\ObjectManager.hpp bindings\bindings\Geode\modify\ObjectToolbox.hpp bindings\bindings\Geode\modify\OnlineListDelegate.hpp bindings\bindings\Geode\modify\OpacityEffectAction.hpp bindings\bindings\Geode\modify\OptionsCell.hpp bindings\bindings\Geode\modify\OptionsLayer.hpp bindings\bindings\Geode\modify\OptionsObject.hpp bindings\bindings\Geode\modify\OptionsObjectDelegate.hpp bindings\bindings\Geode\modify\OptionsScrollLayer.hpp bindings\bindings\Geode\modify\ParentalOptionsLayer.hpp bindings\bindings\Geode\modify\ParticleGameObject.hpp bindings\bindings\Geode\modify\ParticlePreviewLayer.hpp bindings\bindings\Geode\modify\PauseLayer.hpp bindings\bindings\Geode\modify\PlatformDownloadDelegate.hpp bindings\bindings\Geode\modify\PlatformToolbox.hpp bindings\bindings\Geode\modify\PlayLayer.hpp bindings\bindings\Geode\modify\PlayerButtonCommand.hpp bindings\bindings\Geode\modify\PlayerCheckpoint.hpp bindings\bindings\Geode\modify\PlayerControlGameObject.hpp bindings\bindings\Geode\modify\PlayerFireBoostSprite.hpp bindings\bindings\Geode\modify\PlayerObject.hpp bindings\bindings\Geode\modify\PointNode.hpp bindings\bindings\Geode\modify\PriceLabel.hpp bindings\bindings\Geode\modify\ProfilePage.hpp bindings\bindings\Geode\modify\PromoInterstitial.hpp bindings\bindings\Geode\modify\PulseEffectAction.hpp bindings\bindings\Geode\modify\PurchaseItemPopup.hpp bindings\bindings\Geode\modify\RandTriggerGameObject.hpp bindings\bindings\Geode\modify\RateDemonLayer.hpp bindings\bindings\Geode\modify\RateLevelDelegate.hpp bindings\bindings\Geode\modify\RateLevelLayer.hpp bindings\bindings\Geode\modify\RateStarsLayer.hpp bindings\bindings\Geode\modify\RetryLevelLayer.hpp bindings\bindings\Geode\modify\Reverb3D.hpp bindings\bindings\Geode\modify\RewardUnlockLayer.hpp bindings\bindings\Geode\modify\RewardedVideoDelegate.hpp bindings\bindings\Geode\modify\RewardsPage.hpp bindings\bindings\Geode\modify\RingObject.hpp bindings\bindings\Geode\modify\RotateGameplayGameObject.hpp bindings\bindings\Geode\modify\SFXBrowser.hpp bindings\bindings\Geode\modify\SFXBrowserDelegate.hpp bindings\bindings\Geode\modify\SFXFolderObject.hpp bindings\bindings\Geode\modify\SFXInfoObject.hpp bindings\bindings\Geode\modify\SFXSearchResult.hpp bindings\bindings\Geode\modify\SFXStateContainer.hpp bindings\bindings\Geode\modify\SFXTriggerGameObject.hpp bindings\bindings\Geode\modify\SFXTriggerInstance.hpp bindings\bindings\Geode\modify\SFXTriggerState.hpp bindings\bindings\Geode\modify\SavedActiveObjectState.hpp bindings\bindings\Geode\modify\SavedObjectStateRef.hpp bindings\bindings\Geode\modify\SavedSpecialObjectState.hpp bindings\bindings\Geode\modify\ScrollingLayer.hpp bindings\bindings\Geode\modify\SearchButton.hpp bindings\bindings\Geode\modify\SearchSFXPopup.hpp bindings\bindings\Geode\modify\SecretGame01Layer.hpp bindings\bindings\Geode\modify\SecretLayer.hpp bindings\bindings\Geode\modify\SecretLayer2.hpp bindings\bindings\Geode\modify\SecretLayer3.hpp bindings\bindings\Geode\modify\SecretLayer4.hpp bindings\bindings\Geode\modify\SecretLayer5.hpp bindings\bindings\Geode\modify\SecretLayer6.hpp bindings\bindings\Geode\modify\SecretNumberLayer.hpp bindings\bindings\Geode\modify\SecretRewardsLayer.hpp bindings\bindings\Geode\modify\SelectArtDelegate.hpp bindings\bindings\Geode\modify\SelectArtLayer.hpp bindings\bindings\Geode\modify\SelectEventLayer.hpp bindings\bindings\Geode\modify\SelectFontLayer.hpp bindings\bindings\Geode\modify\SelectListIconDelegate.hpp bindings\bindings\Geode\modify\SelectListIconLayer.hpp bindings\bindings\Geode\modify\SelectPremadeDelegate.hpp bindings\bindings\Geode\modify\SelectPremadeLayer.hpp bindings\bindings\Geode\modify\SelectSFXSortDelegate.hpp bindings\bindings\Geode\modify\SelectSFXSortLayer.hpp bindings\bindings\Geode\modify\SelectSettingDelegate.hpp bindings\bindings\Geode\modify\SelectSettingLayer.hpp bindings\bindings\Geode\modify\SequenceTriggerGameObject.hpp bindings\bindings\Geode\modify\SequenceTriggerState.hpp bindings\bindings\Geode\modify\SetColorIDPopup.hpp bindings\bindings\Geode\modify\SetFolderPopup.hpp bindings\bindings\Geode\modify\SetGroupIDLayer.hpp bindings\bindings\Geode\modify\SetIDPopup.hpp bindings\bindings\Geode\modify\SetIDPopupDelegate.hpp bindings\bindings\Geode\modify\SetItemIDLayer.hpp bindings\bindings\Geode\modify\SetLevelOrderPopup.hpp bindings\bindings\Geode\modify\SetTargetIDLayer.hpp bindings\bindings\Geode\modify\SetTextPopup.hpp bindings\bindings\Geode\modify\SetTextPopupDelegate.hpp bindings\bindings\Geode\modify\SetupAdvFollowEditPhysicsPopup.hpp bindings\bindings\Geode\modify\SetupAdvFollowPopup.hpp bindings\bindings\Geode\modify\SetupAdvFollowRetargetPopup.hpp bindings\bindings\Geode\modify\SetupAnimSettingsPopup.hpp bindings\bindings\Geode\modify\SetupAnimationPopup.hpp bindings\bindings\Geode\modify\SetupAreaAnimTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaFadeTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaMoveTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaRotateTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaTintTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaTransformTriggerPopup.hpp bindings\bindings\Geode\modify\SetupAreaTriggerPopup.hpp bindings\bindings\Geode\modify\SetupArtSwitchPopup.hpp bindings\bindings\Geode\modify\SetupAudioLineGuidePopup.hpp bindings\bindings\Geode\modify\SetupAudioTriggerPopup.hpp bindings\bindings\Geode\modify\SetupBGSpeedTrigger.hpp bindings\bindings\Geode\modify\SetupCameraEdgePopup.hpp bindings\bindings\Geode\modify\SetupCameraGuidePopup.hpp bindings\bindings\Geode\modify\SetupCameraModePopup.hpp bindings\bindings\Geode\modify\SetupCameraOffsetTrigger.hpp bindings\bindings\Geode\modify\SetupCameraRotatePopup.hpp bindings\bindings\Geode\modify\SetupCameraRotatePopup2.hpp bindings\bindings\Geode\modify\SetupCheckpointPopup.hpp bindings\bindings\Geode\modify\SetupCoinLayer.hpp bindings\bindings\Geode\modify\SetupCollisionStateTriggerPopup.hpp bindings\bindings\Geode\modify\SetupCollisionTriggerPopup.hpp bindings\bindings\Geode\modify\SetupCountTriggerPopup.hpp bindings\bindings\Geode\modify\SetupDashRingPopup.hpp bindings\bindings\Geode\modify\SetupEndPopup.hpp bindings\bindings\Geode\modify\SetupEnterEffectPopup.hpp bindings\bindings\Geode\modify\SetupEnterTriggerPopup.hpp bindings\bindings\Geode\modify\SetupEventLinkPopup.hpp bindings\bindings\Geode\modify\SetupForceBlockPopup.hpp bindings\bindings\Geode\modify\SetupGameplayOffsetPopup.hpp bindings\bindings\Geode\modify\SetupGradientPopup.hpp bindings\bindings\Geode\modify\SetupGravityModPopup.hpp bindings\bindings\Geode\modify\SetupGravityTriggerPopup.hpp bindings\bindings\Geode\modify\SetupInstantCollisionTriggerPopup.hpp bindings\bindings\Geode\modify\SetupInstantCountPopup.hpp bindings\bindings\Geode\modify\SetupInteractObjectPopup.hpp bindings\bindings\Geode\modify\SetupItemCompareTriggerPopup.hpp bindings\bindings\Geode\modify\SetupItemEditTriggerPopup.hpp bindings\bindings\Geode\modify\SetupKeyframeAnimPopup.hpp bindings\bindings\Geode\modify\SetupKeyframePopup.hpp bindings\bindings\Geode\modify\SetupMGTrigger.hpp bindings\bindings\Geode\modify\SetupMoveCommandPopup.hpp bindings\bindings\Geode\modify\SetupObjectControlPopup.hpp bindings\bindings\Geode\modify\SetupObjectOptions2Popup.hpp bindings\bindings\Geode\modify\SetupObjectOptionsPopup.hpp bindings\bindings\Geode\modify\SetupObjectTogglePopup.hpp bindings\bindings\Geode\modify\SetupOpacityPopup.hpp bindings\bindings\Geode\modify\SetupOptionsTriggerPopup.hpp bindings\bindings\Geode\modify\SetupPersistentItemTriggerPopup.hpp bindings\bindings\Geode\modify\SetupPickupTriggerPopup.hpp bindings\bindings\Geode\modify\SetupPlatformerEndPopup.hpp bindings\bindings\Geode\modify\SetupPlayerControlPopup.hpp bindings\bindings\Geode\modify\SetupPortalPopup.hpp bindings\bindings\Geode\modify\SetupPulsePopup.hpp bindings\bindings\Geode\modify\SetupRandAdvTriggerPopup.hpp bindings\bindings\Geode\modify\SetupRandTriggerPopup.hpp bindings\bindings\Geode\modify\SetupResetTriggerPopup.hpp bindings\bindings\Geode\modify\SetupReverbPopup.hpp bindings\bindings\Geode\modify\SetupRotateCommandPopup.hpp bindings\bindings\Geode\modify\SetupRotateGameplayPopup.hpp bindings\bindings\Geode\modify\SetupRotatePopup.hpp bindings\bindings\Geode\modify\SetupSFXEditPopup.hpp bindings\bindings\Geode\modify\SetupSFXPopup.hpp bindings\bindings\Geode\modify\SetupSequenceTriggerPopup.hpp bindings\bindings\Geode\modify\SetupShaderEffectPopup.hpp bindings\bindings\Geode\modify\SetupShakePopup.hpp bindings\bindings\Geode\modify\SetupSmartBlockLayer.hpp bindings\bindings\Geode\modify\SetupSmartTemplateLayer.hpp bindings\bindings\Geode\modify\SetupSongTriggerPopup.hpp bindings\bindings\Geode\modify\SetupSpawnParticlePopup.hpp bindings\bindings\Geode\modify\SetupSpawnPopup.hpp bindings\bindings\Geode\modify\SetupStaticCameraPopup.hpp bindings\bindings\Geode\modify\SetupStopTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTeleportPopup.hpp bindings\bindings\Geode\modify\SetupTimeWarpPopup.hpp bindings\bindings\Geode\modify\SetupTimerControlTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTimerEventTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTimerTriggerPopup.hpp bindings\bindings\Geode\modify\SetupTouchTogglePopup.hpp bindings\bindings\Geode\modify\SetupTransformPopup.hpp bindings\bindings\Geode\modify\SetupTriggerPopup.hpp bindings\bindings\Geode\modify\SetupZoomTriggerPopup.hpp bindings\bindings\Geode\modify\ShaderGameObject.hpp bindings\bindings\Geode\modify\ShaderLayer.hpp bindings\bindings\Geode\modify\ShardsPage.hpp bindings\bindings\Geode\modify\ShareCommentDelegate.hpp bindings\bindings\Geode\modify\ShareCommentLayer.hpp bindings\bindings\Geode\modify\ShareLevelLayer.hpp bindings\bindings\Geode\modify\ShareLevelSettingsLayer.hpp bindings\bindings\Geode\modify\ShareListLayer.hpp bindings\bindings\Geode\modify\SimpleObject.hpp bindings\bindings\Geode\modify\SimplePlayer.hpp bindings\bindings\Geode\modify\SlideInLayer.hpp bindings\bindings\Geode\modify\Slider.hpp bindings\bindings\Geode\modify\SliderDelegate.hpp bindings\bindings\Geode\modify\SliderThumb.hpp bindings\bindings\Geode\modify\SliderTouchLogic.hpp bindings\bindings\Geode\modify\SmartGameObject.hpp bindings\bindings\Geode\modify\SmartPrefabResult.hpp bindings\bindings\Geode\modify\SmartTemplateCell.hpp bindings\bindings\Geode\modify\SongCell.hpp bindings\bindings\Geode\modify\SongChannelState.hpp bindings\bindings\Geode\modify\SongInfoLayer.hpp bindings\bindings\Geode\modify\SongInfoObject.hpp bindings\bindings\Geode\modify\SongObject.hpp bindings\bindings\Geode\modify\SongOptionsLayer.hpp bindings\bindings\Geode\modify\SongPlaybackDelegate.hpp bindings\bindings\Geode\modify\SongSelectNode.hpp bindings\bindings\Geode\modify\SongTriggerGameObject.hpp bindings\bindings\Geode\modify\SongTriggerState.hpp bindings\bindings\Geode\modify\SongsLayer.hpp bindings\bindings\Geode\modify\Sound.hpp bindings\bindings\Geode\modify\SoundGroup.hpp bindings\bindings\Geode\modify\SoundStateContainer.hpp bindings\bindings\Geode\modify\SpawnParticleGameObject.hpp bindings\bindings\Geode\modify\SpawnTriggerAction.hpp bindings\bindings\Geode\modify\SpawnTriggerGameObject.hpp bindings\bindings\Geode\modify\SpecialAnimGameObject.hpp bindings\bindings\Geode\modify\SpriteAnimationManager.hpp bindings\bindings\Geode\modify\SpriteDescription.hpp bindings\bindings\Geode\modify\SpritePartDelegate.hpp bindings\bindings\Geode\modify\StarInfoPopup.hpp bindings\bindings\Geode\modify\StartPosObject.hpp bindings\bindings\Geode\modify\StatsCell.hpp bindings\bindings\Geode\modify\StatsLayer.hpp bindings\bindings\Geode\modify\StatsObject.hpp bindings\bindings\Geode\modify\SupportLayer.hpp bindings\bindings\Geode\modify\System.hpp bindings\bindings\Geode\modify\TOSPopup.hpp bindings\bindings\Geode\modify\TableView.hpp bindings\bindings\Geode\modify\TableViewCell.hpp bindings\bindings\Geode\modify\TableViewCellDelegate.hpp bindings\bindings\Geode\modify\TableViewDataSource.hpp bindings\bindings\Geode\modify\TableViewDelegate.hpp bindings\bindings\Geode\modify\TeleportPortalObject.hpp bindings\bindings\Geode\modify\TextAlertPopup.hpp bindings\bindings\Geode\modify\TextArea.hpp bindings\bindings\Geode\modify\TextAreaDelegate.hpp bindings\bindings\Geode\modify\TextGameObject.hpp bindings\bindings\Geode\modify\TextInputDelegate.hpp bindings\bindings\Geode\modify\TextStyleSection.hpp bindings\bindings\Geode\modify\TimerItem.hpp bindings\bindings\Geode\modify\TimerTriggerAction.hpp bindings\bindings\Geode\modify\TimerTriggerGameObject.hpp bindings\bindings\Geode\modify\ToggleTriggerAction.hpp bindings\bindings\Geode\modify\TopArtistsLayer.hpp bindings\bindings\Geode\modify\TouchToggleAction.hpp bindings\bindings\Geode\modify\TransformTriggerGameObject.hpp bindings\bindings\Geode\modify\TriggerControlGameObject.hpp bindings\bindings\Geode\modify\TriggerEffectDelegate.hpp bindings\bindings\Geode\modify\TutorialLayer.hpp bindings\bindings\Geode\modify\TutorialPopup.hpp bindings\bindings\Geode\modify\UIButtonConfig.hpp bindings\bindings\Geode\modify\UILayer.hpp bindings\bindings\Geode\modify\UIObjectSettingsPopup.hpp bindings\bindings\Geode\modify\UIOptionsLayer.hpp bindings\bindings\Geode\modify\UIPOptionsLayer.hpp bindings\bindings\Geode\modify\UISaveLoadLayer.hpp bindings\bindings\Geode\modify\UISettingsGameObject.hpp bindings\bindings\Geode\modify\URLCell.hpp bindings\bindings\Geode\modify\URLViewLayer.hpp bindings\bindings\Geode\modify\UndoObject.hpp bindings\bindings\Geode\modify\UpdateAccountSettingsPopup.hpp bindings\bindings\Geode\modify\UploadActionDelegate.hpp bindings\bindings\Geode\modify\UploadActionPopup.hpp bindings\bindings\Geode\modify\UploadListPopup.hpp bindings\bindings\Geode\modify\UploadMessageDelegate.hpp bindings\bindings\Geode\modify\UploadPopup.hpp bindings\bindings\Geode\modify\UploadPopupDelegate.hpp bindings\bindings\Geode\modify\UserInfoDelegate.hpp bindings\bindings\Geode\modify\UserListDelegate.hpp bindings\bindings\Geode\modify\VideoOptionsLayer.hpp bindings\bindings\Geode\modify\WorldLevelPage.hpp bindings\bindings\Geode\modify\WorldSelectLayer.hpp bindings\bindings\Geode\modify\ZipUtils.hpp bindings\bindings\Geode\modify\tk_spline.hpp bindings\bindings\Geode\modify\xml_document.hpp bindings\bindings\Geode\modify\xml_node.hpp | ${cmake_ninja_workdir}bindings\bindings\Geode\CodegenData.txt ${cmake_ninja_workdir}bindings\bindings\Geode\GeneratedBinding.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\GeneratedModify.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\GeneratedPredeclare.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\GeneratedSource.cpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AccountHelpLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AccountLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AccountLoginLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AccountRegisterLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AchievementBar.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AchievementCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AchievementManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AchievementNotifier.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AchievementsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AdToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AdvFollowSetup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AdvancedFollowEditObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AdvancedFollowInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AdvancedFollowTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AnimatedGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AnimatedShopKeeper.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AnimatedSpriteDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AppDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ArtTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ArtistCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AudioAssetsBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AudioEffectsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\AudioLineGuideGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BitmapFontCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BonusDropdown.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BoomListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BoomListView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BoomScrollLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BoomScrollLayerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BrowseSmartKeyLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\BrowseSmartTemplateLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ButtonPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ButtonSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CAState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCAlertCircle.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCAnimateFrameCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCAnimatedSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCBlockLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCCircleAlert.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCCircleWave.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCCircleWaveDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCContentLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCCountdown.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCCounterLabel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCExtenderNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCIndexPath.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCLightFlash.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCLightStrip.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCMenuItemSpriteExtra.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCMenuItemToggler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCMoveCNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCNodeContainer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCPartAnimSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCScrollLayerExt.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCScrollLayerExtDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCSpriteCOpacity.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCSpriteGrayscale.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCSpritePart.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCSpritePlus.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCSpriteWithHue.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCTextInputNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CCURLObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CameraTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ChallengeNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ChallengesPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ChanceObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ChanceTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CharacterColorDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CharacterColorPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CheckpointGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CheckpointObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CollisionBlockPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CollisionTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorAction2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorActionSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorChannelSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorSelectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorSelectLiveOverlay.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorSelectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ColorSetupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CommentCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CommentUploadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CommunityCreditNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CommunityCreditsPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ConfigureHSVWidget.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ConfigureValuePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ConfigureValuePopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CountTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CountTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CreateGuidelinesLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CreateMenuItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CreateParticlePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CreatorLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CurrencyRewardDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CurrencyRewardLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CurrencySprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomListView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomMusicCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSFXCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSFXDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSFXWidget.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSongCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSongDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSongLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSongLayerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomSongWidget.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomizeObjectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\CustomizeObjectSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DailyLevelNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DailyLevelPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DashRingObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DelayedSpawnNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DemonFilterDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DemonFilterSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DemonInfoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DialogDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DialogLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DialogObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DownloadMessageDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DrawGridLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DungeonBarsSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DynamicBitset.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DynamicMoveCalculation.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DynamicObjectAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\DynamicScrollDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditButtonBar.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditGameObjectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditTriggersPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditorOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditorPauseLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EditorUI.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EffectGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EffectManagerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EndLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EndPortalObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EndTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EnhancedGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EnhancedTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EnterEffectAnimValue.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EnterEffectInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EnterEffectObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EventLinkTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\EventTriggerInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ExplodeItemNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ExplodeItemSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ExtendedLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FLAlertLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FLAlertLayerProtocol.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODAudioEngine.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODAudioState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODLevelVisualizer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODMusic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODQueuedEffect.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODQueuedMusic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODSound.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODSoundState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FMODSoundTween.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FRequestProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FileOperation.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FileSaveManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FindBPMLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FindObjectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FollowRewardPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FontObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ForceBlockGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FriendRequestDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FriendRequestPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\FriendsProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountBackupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountLoginDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountRegisterDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountSettingsDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountSettingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAccountSyncDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJActionManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJAssetDownloadAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJBaseGameLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJBigSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJBigSpriteNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJChallengeDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJChallengeItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJChestSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJColorSetupLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJComment.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJCommentListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJDailyLevelDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJDifficultySprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJDropDownLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJDropDownLayerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJEffectManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJFlyGroundLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJFollowCommandLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJFriendRequest.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJGameLevel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJGameLoadingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJGameState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJGarageLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJGradientLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJGroundLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJHttpResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJItemIcon.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJLevelList.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJLevelScoreCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJLocalLevelScoreCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJLocalScore.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMGLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMPDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMapObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMapPack.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMessageCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMessagePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMoreGamesLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJMultiplayerManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJObjectDecoder.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJOnlineRewardDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPFollowCommandLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPathPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPathRewardPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPathSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPathsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPointDouble.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPromoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJPurchaseDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRequestCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRewardDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRewardItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRewardObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRobotSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRotateCommandLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRotationControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJRotationControlDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJScaleControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJScaleControlDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJScoreCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSearchObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJShaderState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJShopLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSmartBlockPreview.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSmartBlockPreviewSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSmartPrefab.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSmartTemplate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSongBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSpecialColorSelect.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSpecialColorSelectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSpiderSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJSpriteColor.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJStoreItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJTransformControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJTransformControlDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJTransformState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJUINode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJUnlockableItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJUserCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJUserMessage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJUserScore.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJValueTween.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJWorldNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GJWriteMessagePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameEffectsManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameLevelManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameLevelOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameObjectCopy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameObjectEditorState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameObjectPhysics.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameOptionsTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameRateDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameStatsManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GameToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GauntletLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GauntletNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GauntletSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GauntletSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GhostTrailEffect.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GooglePlayDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GooglePlayManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GradientTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GraphicsReloadLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GravityEffectSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\GroupCommandObject2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\HSVLiveOverlay.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\HSVWidgetDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\HSVWidgetPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\HardStreak.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\InfoAlertButton.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\InfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\InheritanceNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ItemInfoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ItemTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\KeybindingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\KeybindingsManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\KeyframeAnimTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\KeyframeGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\KeyframeObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LabelGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LeaderboardManagerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LeaderboardsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelAreaInnerLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelAreaLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelBrowserLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelCommentDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelDeleteDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelDownloadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelEditorLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelFeatureLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelLeaderboard.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelListCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelListDeleteDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelManagerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelOptionsLayer2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelSearchLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelSettingsDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelSettingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelSettingsObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelTools.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelUpdateDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LevelUploadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LikeItemDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LikeItemLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ListButtonBar.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ListButtonBarDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ListButtonPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ListCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ListUploadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LoadingCircle.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LoadingCircleSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LoadingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\LocalLevelManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MPLobbyLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MapPackCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MapSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MenuGameLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MenuLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MessageListDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MessagesProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MoreOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MoreSearchLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MoreVideoOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MultiTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MultilineBitmapFont.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MultiplayerLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicArtistObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicBrowserDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicDelegateHandler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicDownloadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicDownloadManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\MusicSearchResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\NCSInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\NewgroundsInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\NodePoint.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\NumberInputDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\NumberInputLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OBB2D.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ObjectControlGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ObjectManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ObjectToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OnlineListDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OpacityEffectAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OptionsCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OptionsObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OptionsObjectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\OptionsScrollLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ParentalOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ParticleGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ParticlePreviewLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PauseLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlatformDownloadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlatformToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlayLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlayerButtonCommand.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlayerCheckpoint.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlayerControlGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlayerFireBoostSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PlayerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PointNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PriceLabel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PromoInterstitial.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PulseEffectAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\PurchaseItemPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RandTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RateDemonLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RateLevelDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RateLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RateStarsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RetryLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RewardUnlockLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RewardedVideoDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RewardsPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RingObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\RotateGameplayGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXBrowserDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXFolderObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXInfoObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXSearchResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXStateContainer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXTriggerInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SFXTriggerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SavedActiveObjectState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SavedObjectStateRef.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SavedSpecialObjectState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ScrollingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SearchButton.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SearchSFXPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretGame01Layer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretLayer2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretLayer3.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretLayer4.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretLayer5.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretLayer6.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretNumberLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SecretRewardsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectArtDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectArtLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectEventLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectFontLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectListIconDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectListIconLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectPremadeDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectPremadeLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectSFXSortDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectSFXSortLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectSettingDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SelectSettingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SequenceTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SequenceTriggerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetColorIDPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetFolderPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetGroupIDLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetIDPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetIDPopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetItemIDLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetLevelOrderPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetTargetIDLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetTextPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetTextPopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAdvFollowEditPhysicsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAdvFollowPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAdvFollowRetargetPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAnimSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAnimationPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaAnimTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaFadeTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaMoveTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaRotateTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaTintTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaTransformTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAreaTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupArtSwitchPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAudioLineGuidePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupAudioTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupBGSpeedTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCameraEdgePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCameraGuidePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCameraModePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCameraOffsetTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCameraRotatePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCameraRotatePopup2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCheckpointPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCoinLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCollisionStateTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCollisionTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupCountTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupDashRingPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupEndPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupEnterEffectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupEnterTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupEventLinkPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupForceBlockPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupGameplayOffsetPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupGradientPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupGravityModPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupGravityTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupInstantCollisionTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupInstantCountPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupInteractObjectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupItemCompareTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupItemEditTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupKeyframeAnimPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupKeyframePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupMGTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupMoveCommandPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupObjectControlPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupObjectOptions2Popup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupObjectOptionsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupObjectTogglePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupOpacityPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupOptionsTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupPersistentItemTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupPickupTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupPlatformerEndPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupPlayerControlPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupPortalPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupPulsePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupRandAdvTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupRandTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupResetTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupReverbPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupRotateCommandPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupRotateGameplayPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupRotatePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSFXEditPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSFXPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSequenceTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupShaderEffectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupShakePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSmartBlockLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSmartTemplateLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSongTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSpawnParticlePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupSpawnPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupStaticCameraPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupStopTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTeleportPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTimeWarpPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTimerControlTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTimerEventTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTimerTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTouchTogglePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTransformPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SetupZoomTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShaderGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShaderLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShardsPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShareCommentDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShareCommentLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShareLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShareLevelSettingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ShareListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SimpleObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SimplePlayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SlideInLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\Slider.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SliderDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SliderThumb.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SliderTouchLogic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SmartGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SmartPrefabResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SmartTemplateCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongChannelState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongInfoObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongPlaybackDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongSelectNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongTriggerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SongsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SoundStateContainer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpawnParticleGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpawnTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpawnTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpecialAnimGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpriteAnimationManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpriteDescription.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SpritePartDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\Standalones.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\StarInfoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\StartPosObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\StatsCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\StatsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\StatsObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\SupportLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TOSPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TableView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TableViewCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TableViewCellDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TableViewDataSource.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TableViewDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TeleportPortalObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TextAlertPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TextArea.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TextAreaDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TextGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TextInputDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TextStyleSection.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TimerItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TimerTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TimerTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\ToggleTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TopArtistsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TouchToggleAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TransformTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TriggerControlGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TriggerEffectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TutorialLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\TutorialPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UIButtonConfig.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UILayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UIObjectSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UIOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UIPOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UISaveLoadLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UISettingsGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\URLCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\URLViewLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UndoObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UpdateAccountSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UploadActionDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UploadActionPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UploadListPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UploadMessageDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UploadPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UploadPopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UserInfoDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\UserListDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\VideoOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\WorldLevelPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\WorldSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\binding\tk_spline.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AccountHelpLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AccountLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AccountLoginLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AccountRegisterLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AchievementBar.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AchievementCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AchievementManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AchievementNotifier.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AchievementsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AdToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AdvFollowSetup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AdvancedFollowEditObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AdvancedFollowInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AdvancedFollowTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AnimatedGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AnimatedShopKeeper.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AnimatedSpriteDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AppDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ArtTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ArtistCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AudioAssetsBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AudioEffectsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\AudioLineGuideGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BitmapFontCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BonusDropdown.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BoomListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BoomListView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BoomScrollLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BoomScrollLayerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BrowseSmartKeyLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\BrowseSmartTemplateLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ButtonPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ButtonSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CAState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCActionCamera.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCActionEase.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCActionInstant.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCActionInterval.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCActionManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAlertCircle.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAnimate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAnimateFrameCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAnimatedSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAnimation.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCAnimationCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCApplication.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCArray.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCBezierBy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCBezierTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCBlink.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCBlockLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCallFunc.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCallFuncN.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCallFuncND.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCallFuncO.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCircleAlert.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCircleWave.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCircleWaveDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCClippingNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCConfiguration.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCContentLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCContentManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCControlColourPicker.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCControlHuePicker.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCControlSaturationBrightnessPicker.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCControlUtils.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCountdown.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCCounterLabel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCDelayTime.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCDictionary.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCDirector.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCDisplayLinkDirector.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCDrawNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEGLView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEGLViewProtocol.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBackIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBackInOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBackOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBounce.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBounceIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBounceInOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseBounceOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseElastic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseElasticIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseElasticInOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseElasticOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseExponentialIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseExponentialInOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseExponentialOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseInOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseRateAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseSineIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseSineInOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCEaseSineOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCExtenderNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCFadeIn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCFadeOut.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCFadeTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCFileUtils.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCFiniteTimeAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCGLProgram.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCHide.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCHttpClient.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCHttpRequest.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCHttpResponse.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCIMEDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCIMEDispatcher.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCImage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCIndexPath.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCJumpBy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCJumpTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCKeyboardDispatcher.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCKeypadDispatcher.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLabelBMFont.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLabelTTF.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLayerColor.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLayerGradient.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLayerRGBA.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLightFlash.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLightStrip.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCLightning.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMenu.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMenuItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMenuItemImage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMenuItemSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMenuItemSpriteExtra.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMenuItemToggler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMotionStreak.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMouseDispatcher.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMouseHandler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMoveBy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMoveCNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCMoveTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCNodeContainer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCNodeRGBA.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCOrbitCamera.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCPartAnimSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCParticleExplosion.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCParticleFire.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCParticleRain.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCParticleSnow.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCParticleSystem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCParticleSystemQuad.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCPoolManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCProgressTimer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCRemoveSelf.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCRenderTexture.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCRepeat.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCRepeatForever.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCRotateBy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCRotateTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScale9Sprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScaleBy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScaleTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScene.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScheduler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScriptEngineManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScrollLayerExt.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCScrollLayerExtDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSequence.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSet.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCShaderCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCShow.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSkewBy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSkewTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpawn.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpeed.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpriteBatchNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpriteCOpacity.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpriteFrame.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpriteFrameCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpriteGrayscale.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpritePart.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpritePlus.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCSpriteWithHue.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCString.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTargetedTouchHandler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTextFieldTTF.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTextInputNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTexture2D.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTextureAtlas.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTextureCache.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTintTo.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTouch.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTouchDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTouchDispatcher.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTouchHandler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionCrossFade.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFade.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFadeBL.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFadeDown.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFadeTR.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFadeUp.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFlipAngular.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFlipX.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionFlipY.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionJumpZoom.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionMoveInB.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionMoveInL.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionMoveInR.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionMoveInT.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionRotoZoom.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionScene.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionShrinkGrow.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionSlideInB.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionSlideInL.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionSlideInR.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionSlideInT.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionSplitCols.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionSplitRows.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionTurnOffTiles.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionZoomFlipAngular.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionZoomFlipX.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTransitionZoomFlipY.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCTurnOffTiles.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCURLObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CCZone.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CameraTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ChallengeNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ChallengesPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ChanceObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ChanceTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\Channel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ChannelControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ChannelGroup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CharacterColorDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CharacterColorPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CheckpointGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CheckpointObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CollisionBlockPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CollisionTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorAction2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorActionSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorChannelSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorSelectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorSelectLiveOverlay.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorSelectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ColorSetupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CommentCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CommentUploadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CommunityCreditNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CommunityCreditsPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ConfigureHSVWidget.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ConfigureValuePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ConfigureValuePopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CountTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CountTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CreateGuidelinesLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CreateMenuItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CreateParticlePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CreatorLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CurrencyRewardDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CurrencyRewardLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CurrencySprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomListView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomMusicCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSFXCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSFXDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSFXWidget.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSongCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSongDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSongLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSongLayerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomSongWidget.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomizeObjectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\CustomizeObjectSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DSP.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DSPConnection.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DS_Dictionary.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DailyLevelNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DailyLevelPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DashRingObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DelayedSpawnNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DemonFilterDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DemonFilterSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DemonInfoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DialogDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DialogLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DialogObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DownloadMessageDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DrawGridLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DungeonBarsSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DynamicBitset.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DynamicMoveCalculation.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DynamicObjectAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\DynamicScrollDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditButtonBar.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditGameObjectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditTriggersPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditorOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditorPauseLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EditorUI.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EffectGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EffectManagerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EndLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EndPortalObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EndTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EnhancedGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EnhancedTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EnterEffectAnimValue.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EnterEffectInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EnterEffectObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EventLinkTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\EventTriggerInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ExplodeItemNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ExplodeItemSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ExtendedLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FLAlertLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FLAlertLayerProtocol.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODAudioEngine.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODAudioState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODLevelVisualizer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODMusic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODQueuedEffect.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODQueuedMusic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODSound.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODSoundState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FMODSoundTween.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FRequestProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FileOperation.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FileSaveManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FindBPMLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FindObjectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FollowRewardPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FontObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ForceBlockGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FriendRequestDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FriendRequestPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\FriendsProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountBackupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountLoginDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountRegisterDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountSettingsDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountSettingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAccountSyncDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJActionManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJAssetDownloadAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJBaseGameLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJBigSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJBigSpriteNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJChallengeDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJChallengeItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJChestSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJColorSetupLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJComment.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJCommentListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJDailyLevelDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJDifficultySprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJDropDownLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJDropDownLayerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJEffectManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJFlyGroundLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJFollowCommandLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJFriendRequest.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJGameLevel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJGameLoadingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJGameState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJGarageLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJGradientLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJGroundLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJHttpResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJItemIcon.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJLevelList.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJLevelScoreCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJLocalLevelScoreCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJLocalScore.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMGLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMPDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMapObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMapPack.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMessageCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMessagePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMoreGamesLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJMultiplayerManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJObjectDecoder.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJOnlineRewardDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPFollowCommandLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPathPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPathRewardPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPathSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPathsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPointDouble.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPromoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJPurchaseDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRequestCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRewardDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRewardItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRewardObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRobotSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRotateCommandLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRotationControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJRotationControlDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJScaleControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJScaleControlDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJScoreCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSearchObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJShaderState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJShopLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSmartBlockPreview.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSmartBlockPreviewSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSmartPrefab.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSmartTemplate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSongBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSpecialColorSelect.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSpecialColorSelectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSpiderSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJSpriteColor.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJStoreItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJTransformControl.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJTransformControlDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJTransformState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJUINode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJUnlockableItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJUserCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJUserMessage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJUserScore.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJValueTween.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJWorldNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GJWriteMessagePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameEffectsManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameLevelManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameLevelOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameObjectCopy.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameObjectEditorState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameObjectPhysics.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameOptionsTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameRateDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameStatsManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GameToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GauntletLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GauntletNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GauntletSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GauntletSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\Geometry.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GhostTrailEffect.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GooglePlayDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GooglePlayManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GradientTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GraphicsReloadLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GravityEffectSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\GroupCommandObject2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\HSVLiveOverlay.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\HSVWidgetDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\HSVWidgetPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\HardStreak.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\InfoAlertButton.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\InfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\InheritanceNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ItemInfoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ItemTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\KeybindingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\KeybindingsManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\KeyframeAnimTriggerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\KeyframeGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\KeyframeObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LabelGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LeaderboardManagerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LeaderboardsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelAreaInnerLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelAreaLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelBrowserLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelCommentDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelDeleteDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelDownloadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelEditorLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelFeatureLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelLeaderboard.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelListCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelListDeleteDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelManagerDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelOptionsLayer2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelSearchLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelSettingsDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelSettingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelSettingsObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelTools.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelUpdateDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LevelUploadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LikeItemDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LikeItemLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ListButtonBar.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ListButtonBarDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ListButtonPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ListCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ListUploadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LoadingCircle.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LoadingCircleSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LoadingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\LocalLevelManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MPLobbyLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MapPackCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MapSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MenuGameLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MenuLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MessageListDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MessagesProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MoreOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MoreSearchLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MoreVideoOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MultiTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MultilineBitmapFont.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MultiplayerLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicArtistObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicBrowserDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicDelegateHandler.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicDownloadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicDownloadManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\MusicSearchResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\NCSInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\NewgroundsInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\NodePoint.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\NumberInputDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\NumberInputLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OBB2D.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ObjectControlGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ObjectDecoder.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ObjectManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ObjectToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OnlineListDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OpacityEffectAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OptionsCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OptionsObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OptionsObjectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\OptionsScrollLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ParentalOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ParticleGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ParticlePreviewLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PauseLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlatformDownloadDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlatformToolbox.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlayLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlayerButtonCommand.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlayerCheckpoint.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlayerControlGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlayerFireBoostSprite.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PlayerObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PointNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PriceLabel.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ProfilePage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PromoInterstitial.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PulseEffectAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\PurchaseItemPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RandTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RateDemonLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RateLevelDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RateLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RateStarsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RetryLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\Reverb3D.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RewardUnlockLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RewardedVideoDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RewardsPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RingObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\RotateGameplayGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXBrowser.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXBrowserDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXFolderObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXInfoObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXSearchResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXStateContainer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXTriggerInstance.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SFXTriggerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SavedActiveObjectState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SavedObjectStateRef.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SavedSpecialObjectState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ScrollingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SearchButton.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SearchSFXPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretGame01Layer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretLayer2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretLayer3.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretLayer4.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretLayer5.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretLayer6.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretNumberLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SecretRewardsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectArtDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectArtLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectEventLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectFontLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectListIconDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectListIconLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectPremadeDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectPremadeLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectSFXSortDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectSFXSortLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectSettingDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SelectSettingLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SequenceTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SequenceTriggerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetColorIDPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetFolderPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetGroupIDLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetIDPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetIDPopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetItemIDLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetLevelOrderPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetTargetIDLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetTextPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetTextPopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAdvFollowEditPhysicsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAdvFollowPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAdvFollowRetargetPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAnimSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAnimationPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaAnimTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaFadeTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaMoveTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaRotateTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaTintTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaTransformTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAreaTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupArtSwitchPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAudioLineGuidePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupAudioTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupBGSpeedTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCameraEdgePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCameraGuidePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCameraModePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCameraOffsetTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCameraRotatePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCameraRotatePopup2.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCheckpointPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCoinLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCollisionStateTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCollisionTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupCountTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupDashRingPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupEndPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupEnterEffectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupEnterTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupEventLinkPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupForceBlockPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupGameplayOffsetPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupGradientPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupGravityModPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupGravityTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupInstantCollisionTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupInstantCountPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupInteractObjectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupItemCompareTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupItemEditTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupKeyframeAnimPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupKeyframePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupMGTrigger.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupMoveCommandPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupObjectControlPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupObjectOptions2Popup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupObjectOptionsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupObjectTogglePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupOpacityPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupOptionsTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupPersistentItemTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupPickupTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupPlatformerEndPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupPlayerControlPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupPortalPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupPulsePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupRandAdvTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupRandTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupResetTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupReverbPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupRotateCommandPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupRotateGameplayPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupRotatePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSFXEditPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSFXPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSequenceTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupShaderEffectPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupShakePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSmartBlockLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSmartTemplateLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSongTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSpawnParticlePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupSpawnPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupStaticCameraPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupStopTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTeleportPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTimeWarpPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTimerControlTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTimerEventTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTimerTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTouchTogglePopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTransformPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SetupZoomTriggerPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShaderGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShaderLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShardsPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShareCommentDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShareCommentLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShareLevelLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShareLevelSettingsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ShareListLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SimpleObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SimplePlayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SlideInLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\Slider.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SliderDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SliderThumb.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SliderTouchLogic.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SmartGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SmartPrefabResult.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SmartTemplateCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongChannelState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongInfoLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongInfoObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongPlaybackDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongSelectNode.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongTriggerState.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SongsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\Sound.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SoundGroup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SoundStateContainer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpawnParticleGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpawnTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpawnTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpecialAnimGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpriteAnimationManager.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpriteDescription.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SpritePartDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\StarInfoPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\StartPosObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\StatsCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\StatsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\StatsObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\SupportLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\System.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TOSPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TableView.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TableViewCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TableViewCellDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TableViewDataSource.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TableViewDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TeleportPortalObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TextAlertPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TextArea.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TextAreaDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TextGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TextInputDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TextStyleSection.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TimerItem.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TimerTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TimerTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ToggleTriggerAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TopArtistsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TouchToggleAction.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TransformTriggerGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TriggerControlGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TriggerEffectDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TutorialLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\TutorialPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UIButtonConfig.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UILayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UIObjectSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UIOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UIPOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UISaveLoadLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UISettingsGameObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\URLCell.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\URLViewLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UndoObject.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UpdateAccountSettingsPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UploadActionDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UploadActionPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UploadListPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UploadMessageDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UploadPopup.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UploadPopupDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UserInfoDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\UserListDelegate.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\VideoOptionsLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\WorldLevelPage.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\WorldSelectLayer.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\ZipUtils.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\tk_spline.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\xml_document.hpp ${cmake_ninja_workdir}bindings\bindings\Geode\modify\xml_node.hpp: CUSTOM_COMMAND _deps\bindings-src\bindings\2.2074\Cocos2d.bro _deps\bindings-src\bindings\2.2074\Entry.bro _deps\bindings-src\bindings\2.2074\Extras.bro _deps\bindings-src\bindings\2.2074\FMOD.bro _deps\bindings-src\bindings\2.2074\GeometryDash.bro || _deps\fmt-build\fmtd.lib
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\source\repos\Paibotgeode\out\build\x64-debug\bindings\codegen && .\Codegen Win64 C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/bindings-src/bindings/2.2074 C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/bindings/bindings"
  DESC = Running Codegen
  restat = 1

# =============================================================================
# Target aliases.

build GeodeBindings: phony bindings\GeodeBindings.lib

build GeodeBindings.lib: phony bindings\GeodeBindings.lib

build fmt: phony _deps\fmt-build\fmtd.lib

build fmtd.lib: phony _deps\fmt-build\fmtd.lib

build xdBot2: phony paimon.scaler.dll

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug

build all: phony paimon.scaler.dll xdBot2_PACKAGE geode\all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/fmt-build

build _deps\fmt-build\all: phony _deps\fmt-build\fmtd.lib

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/json-build

build _deps\json-build\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/result-build

build _deps\result-build\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/_deps/tuliphook-build

build _deps\tuliphook-build\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/bindings

build bindings\all: phony bindings\GeodeBindings.lib

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/source/repos/Paibotgeode/out/build/x64-debug/geode

build geode\all: phony bindings\all

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompilerABI.c C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseArguments.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FetchContent.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FetchContent\CMakeLists.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindGit.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake C$:\Users\fg906\mod-dev\CMakeLists.txt C$:\Users\fg906\mod-dev\cmake\CPM.cmake C$:\Users\fg906\mod-dev\cmake\GeodeFile.cmake C$:\Users\fg906\mod-dev\cmake\Platform.cmake C$:\Users\fg906\mod-dev\cmake\PlatformDetect.cmake C$:\Users\fg906\source\repos\Paibotgeode\CMakeLists.txt C$:\Users\fg906\source\repos\Paibotgeode\mod.json CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake _deps\bindings-src\CMakeLists.txt _deps\fmt-src\CMakeLists.txt _deps\fmt-src\support\cmake\JoinPaths.cmake _deps\fmt-src\support\cmake\fmt-config.cmake.in _deps\fmt-src\support\cmake\fmt.pc.in _deps\json-src\CMakeLists.txt _deps\result-src\CMakeLists.txt _deps\tuliphook-src\CMakeLists.txt _deps\tuliphook-src\cmake\CPM.cmake cmake\CPM_0.38.7.cmake cmake\CPM_0.40.2.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCCompilerABI.c C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakePackageConfigHelpers.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseArguments.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Bruce-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SDCC-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-C-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\ExternalProject\shared_internal_commands.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FetchContent.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FetchContent\CMakeLists.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindGit.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\WriteBasicConfigVersionFile.cmake C$:\Users\fg906\mod-dev\CMakeLists.txt C$:\Users\fg906\mod-dev\cmake\CPM.cmake C$:\Users\fg906\mod-dev\cmake\GeodeFile.cmake C$:\Users\fg906\mod-dev\cmake\Platform.cmake C$:\Users\fg906\mod-dev\cmake\PlatformDetect.cmake C$:\Users\fg906\source\repos\Paibotgeode\CMakeLists.txt C$:\Users\fg906\source\repos\Paibotgeode\mod.json CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake _deps\bindings-src\CMakeLists.txt _deps\fmt-src\CMakeLists.txt _deps\fmt-src\support\cmake\JoinPaths.cmake _deps\fmt-src\support\cmake\fmt-config.cmake.in _deps\fmt-src\support\cmake\fmt.pc.in _deps\json-src\CMakeLists.txt _deps\result-src\CMakeLists.txt _deps\tuliphook-src\CMakeLists.txt _deps\tuliphook-src\cmake\CPM.cmake cmake\CPM_0.38.7.cmake cmake\CPM_0.40.2.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
