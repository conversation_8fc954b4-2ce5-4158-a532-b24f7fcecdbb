<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>frames</key>
	<dict>
		<key>geode.custom-keybinds/controllerBtn_LB_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{0,0}</string>
			<key>spriteSize</key>
			<string>{29,21}</string>
			<key>spriteSourceSize</key>
			<string>{29,21}</string>
			<key>textureRect</key>
			<string>{{0,0},{29,21}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
		<key>geode.custom-keybinds/controllerBtn_LT_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{0,0}</string>
			<key>spriteSize</key>
			<string>{29,21}</string>
			<key>spriteSourceSize</key>
			<string>{29,21}</string>
			<key>textureRect</key>
			<string>{{0,23},{29,21}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
		<key>geode.custom-keybinds/controllerBtn_RB_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{0,0}</string>
			<key>spriteSize</key>
			<string>{29,21}</string>
			<key>spriteSourceSize</key>
			<string>{29,21}</string>
			<key>textureRect</key>
			<string>{{0,46},{29,21}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
		<key>geode.custom-keybinds/controllerBtn_RT_001.png</key>
		<dict>
			<key>spriteOffset</key>
			<string>{0,0}</string>
			<key>spriteSize</key>
			<string>{29,21}</string>
			<key>spriteSourceSize</key>
			<string>{29,21}</string>
			<key>textureRect</key>
			<string>{{0,69},{29,21}}</string>
			<key>textureRotated</key>
			<false/>
		</dict>
	</dict>
	<key>metadata</key>
	<dict>
		<key>format</key>
		<integer>3</integer>
		<key>realTextureFileName</key>
		<string>geode.custom-keybinds/KB_ControllerSheet.png</string>
		<key>size</key>
		<string>{29,90}</string>
		<key>textureFileName</key>
		<string>geode.custom-keybinds/KB_ControllerSheet.png</string>
	</dict>
</dict>
</plist>