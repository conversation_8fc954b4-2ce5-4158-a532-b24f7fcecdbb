#include "../includes.hpp"

#include <Geode/modify/GJBaseGameLayer.hpp>

class $modify(GJBaseGameLayer) {

    struct Fields {
        int framesHolding[2] = {0, 0}; 
        int framesReleasing[2] = {0, 0};
        bool autoclick[2] = {false, false};
        bool holding[2] = {false, false};
        int lastProcessFrame = -1; // Prevenir procesamiento múltiple por frame
        int performanceCounter = 0; // Contador para optimización
        float lastDeltaTime = 0.0f; // Para sincronización
    };

    void processCommands(float dt) {
        GJBaseGameLayer::processCommands(dt);

        auto& g = Global::get();

        if (!g.autoclicker) return;
        if (!PlayLayer::get()) return;
        // Forzar velocidad completamente normal cuando autoclicker está activo
        Global::forceNormalSpeed();


        // Prevenir procesamiento múltiple por frame
        int currentFrame = Global::getCurrentFrame();
        if (currentFrame == m_fields->lastProcessFrame) return;
        m_fields->lastProcessFrame = currentFrame;

        // Sincronización con delta time para evitar speedhack lento
        m_fields->lastDeltaTime = dt;

        // Optimización: procesar solo cada frame para máxima precisión
        m_fields->performanceCounter++;

        auto& f = m_fields;
        const int holdFor[2] = {g.holdFor, g.holdFor2};
        const int releaseFor[2] = {g.releaseFor, g.releaseFor2};

        // Límite de seguridad para evitar lag
        int processedActions = 0;
        const int MAX_ACTIONS_PER_FRAME = 4;

        for (int i = 0; i < 2 && processedActions < MAX_ACTIONS_PER_FRAME; i++) {
            bool isPlayer1 = (i == 0);
            if ((!g.autoclickerP1 && isPlayer1) || (!g.autoclickerP2 && !isPlayer1)) continue;

            // Verificar límites de seguridad
            if (holdFor[i] <= 0 || releaseFor[i] <= 0) continue;

            if (f->framesReleasing[i] < (releaseFor[i] - 1) / (f->holding[i] ? 2.f : 1.f)) {
                f->framesReleasing[i]++;
                continue;
            }

            if (f->framesHolding[i] < holdFor[i]) {
                if (f->framesHolding[i] == 0) {
                    f->autoclick[i] = true;
                    GJBaseGameLayer::handleButton(true, 1, Macro::flipControls() ? !isPlayer1 : isPlayer1);
                    f->autoclick[i] = false;
                    processedActions++;
                }

                f->framesHolding[i]++;
            }
            else {
                f->autoclick[i] = true;
                GJBaseGameLayer::handleButton(false, 1, Macro::flipControls() ? !isPlayer1 : isPlayer1);
                f->autoclick[i] = false;
                processedActions++;

                f->framesReleasing[i] = 0;
                f->framesHolding[i] = 0;
            }
        }

    }

    void handleButton(bool hold, int button, bool player1) {
        if (button != 1) return GJBaseGameLayer::handleButton(hold, button, player1);

        auto& g = Global::get();
        if (!g.autoclicker) return GJBaseGameLayer::handleButton(hold, button, player1);

        auto& f = m_fields;
        bool realPlayer1 = Macro::flipControls() ? !player1 : player1;
        int i = realPlayer1 ? 0 : 1;

        // Prevenir llamadas recursivas del autoclicker
        if (f->autoclick[i] || (realPlayer1 && !g.autoclickerP1) || (!realPlayer1 && !g.autoclickerP2)) {
            return GJBaseGameLayer::handleButton(hold, button, player1);
        }

        f->holding[i] = hold;

        if (!hold) {
            f->framesHolding[i] = 0;
            f->framesReleasing[i] = 0;
            GJBaseGameLayer::handleButton(hold, button, player1);
        }

    }

    // Función de limpieza para optimizar rendimiento
    void cleanupAutoclicker() {
        auto& f = m_fields;
        for (int i = 0; i < 2; i++) {
            f->framesHolding[i] = 0;
            f->framesReleasing[i] = 0;
            f->autoclick[i] = false;
            f->holding[i] = false;
        }
        f->lastProcessFrame = -1;
        f->performanceCounter = 0;
        f->lastDeltaTime = 0.0f;
    }

};